import { ResetPassword } from "../models/Resetpassword";
import { adminApiService, userUrl } from "./APIService";
import { JSEncrypt } from 'jsencrypt';
const publicKey = process.env.REACT_APP_PUBLIC_ENCRYPT_KEY; 
export const encryptResetPassword = (password: string) => {
	const encrypt = new JSEncrypt();
	encrypt.setPublicKey(publicKey?publicKey:"");
	const encryptedPassword = encrypt.encrypt(password);
	return encryptedPassword;
};
export const Resetpassword = async (PasswordLogId: string, NewPassword: string, ReEnterNewPassword: string) => {
    try {
        const url = `/User/ResetUserPassword?PasswordLogId=${PasswordLogId}&NewPassword=${encodeURIComponent(NewPassword)}&ReEnterNewPassword=${encodeURIComponent(ReEnterNewPassword)}`;
        const response = await adminApiService.get(url);
        if (response.status === 200 && response.data.Success) {
            return response.data; 
        } else {
            return response.data.ErrorMessage ; 
        }
    } catch (error: any) {
        if (error.response) {
            console.error("Error Response:", error.response.data);
            throw new Error(`Error: ${error.response.status} - ${error.response.data}`);
        } else if (error.request) {
            console.error("No response received:", error.request);
            throw new Error('No response received from the server');
        } else {
            console.error("Request setup error:", error.message);
            throw new Error('Error sending password reset request');
        }
    }
};