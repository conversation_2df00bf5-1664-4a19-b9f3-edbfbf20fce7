import React, { useState, useEffect } from "react";
import {
	DataGrid,
	GridColDef,
	GridToolbarContainer,
	GridToolbarColumnsButton,
	GridToolbarFilterButton,
	GridToolbarDensitySelector,
	GridPaginationModel
} from "@mui/x-data-grid";
import {
	Button,
	Menu,
	MenuItem,
	TextField,
	Autocomplete,
	IconButton
} from "@mui/material";
import SaveAltIcon from "@mui/icons-material/SaveAlt";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import loader from "../../assets/loader.gif";
import { getOrganization } from "../../services/OrganizationService";
import { isSidebarOpen, subscribe } from "../adminMenu/sidemenustate";
import { GetAuditLogsBySearch, GetAuditLogsByOrganizationId } from "../../services/AuditLogServices";
import { SearchParams } from "../../models/SearchParams";
import { format } from 'date-fns';
import ClearIcon from '@mui/icons-material/Clear';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { fetchOrganizations } from "../../services/AuditLogServices";
import { fetchUsersList } from "../../services/UserService";
import { Box } from '@mui/material';
import { OrganizationId } from "../common/Home";
import dayjs, { Dayjs } from 'dayjs';
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";

interface AuditLog {
	AuditLogId: string;
	ReferenceId: string;
	Name: string;
	ReferenceType: string;
	OrganizationId: string;
	Type: string;
	CreatedBy: string;
	Browser: string,
	IPAddress: string,
	CreatedDate: string;
}
const categories={
	"UserSession": [
		"Login",
		"Logout"
	],
	"Experiences": [
		"TourCreated",
		"TourUpdated",
		"TourDeleted",
		"TourPublished",
		"AnnouncementCreated",
		"AnnouncementUpdated",
		"AnnouncementDeleted",
		"TooltipCreated",
		"TooltipUpdated",
		"TooltipDeleted",
		"BannerCreated",
		"BannerUpdated",
		"BannerDeleted",
		"ChecklistCreated",
		"ChecklistUpdated",
		"ChecklistDeleted",
		"SurveyCreated",
		"SurveyUpdated",
		"SurveyDeleted",
		"HotspotCreated",
		"HotspotUpdated",
		"HotspotDeleted",
		"HotspotPublished"
	],
	"User": [
		"NewUserCreated",
		"UserUpdated",
		"UserDeactivated",
		"UserReactivated",
	],
	"UserRole": [
		"UserRoleAssigned",
		"UserRoleUpdated",
		"UserRoleDeleted"
	],
	"Account": [
		"AccountCreated",
		"AccountUpdated",
		"AccountDeleted",
	]
}

const SuperAdminAuditLogList: React.FC = () => {
	const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
	const [totalRecords, setTotalRecords] = useState();
	const [loading, setLoading] = useState(false);
	const [referenceTypes, setReferenceTypes] = useState<string[]>([]);
	const [organizationId, setOrganizationId] = useState<string | null>();
	const [orgid, setOrgId] = useState<string | null>(OrganizationId);
	const [types, setTypes] = useState<string[]>([]);
	const [environment, setEnvironment] = useState<string>("");
	const [type, setType] = useState<string | null>(null);
	const [referenceType, setReferenceType] = useState<string | null>(null);
	const [typeOptions, setTypeOptions] = useState<string[]>([]);
	const [name, setName] = useState<string>("");
	const [user, setUser] = useState<string | null>(null);
	const [organizations, setOrganizations] = useState<any[]>([]);
	const [organization, setOrganization] = useState<string>('');
	const [defaultOrganization, setDefaultOrganization] = useState("");  // Store the default organization
    const [orgname, setOrgName] = useState("");  // Name associated with the organization
	const [models, setModels] = useState("");
	const [createdUser, setCreatedUser] = useState<string>('');
	const [nameFilter, setNameFilter] = useState<string>('');
	const [eventType, setEventType] = useState<string>('');
	const [allAuditLogData, setAllAuditLogData] = useState<AuditLog[]>([]);
	const [selectedOrganizationId, setSelectedOrganizationId] = useState<number | null>(null);
	const [users, setUsers] = useState<any[]>([]);
	const [selectedUser, setSelectedUser] = useState(null);
	const [isClearing, setIsClearing] = useState(false);
	const [orderByFields, setOrderByFields] = useState("");
	const [filters, setFilters] = useState([]);
  


	const [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());
	const [paginationModel, setPaginationModel] = useState({
		page: 0,
		pageSize: 15,
	});
	
	const [skip, setskip] = useState(0);
    const [top, settop] = useState(10);
    const [totalcount, setTotalcount] = useState(0);
	const columns: GridColDef[] = [
		{ field: "ReferenceType", headerName: "Category", width: 350, flex: 1,disableColumnMenu: true },
		{field:"Type",headerName:"Event Type",width:350,flex:1,disableColumnMenu: true},
		{ field: "Name", headerName: "Name", width: 350, flex: 1,disableColumnMenu: true },
		{field:"IPAddress", headerName:"IP Address", width:350,flex:1,disableColumnMenu: true},
		{ field: "Browser", headerName: "Browser", width: 300, flex: 1,disableColumnMenu: true },
		{ field: "CreatedBy", headerName: "Created By", width: 300, flex: 1,disableColumnMenu: true },
		{ field: "CreatedDate", headerName: "Created Date", width: 400 ,flex: 1,disableColumnMenu: true },
	];
	
	
	const formatIndianDateTime = (dateString: string) => {
		return format(new Date(dateString), 'dd-MM-yyyy HH:mm:ss');
	};
	useEffect(() => {
		if (referenceType && categories[referenceType as keyof typeof categories]) {
		  setTypeOptions(categories[referenceType as keyof typeof categories]);
		} else {
		  setTypeOptions([]);
		}
		setType("");
	}, [referenceType]);
	// here setting organizationid 
	useEffect(() => {
		const fetchAndSetOrganizations = async () => {
		  const orgData = await fetchOrganizations(setOrganizations, setLoading);
		  if (orgData && orgData.length > 0) {
			setOrganizations(orgData);
			// Set the first organization as the default selected organization
			const defaultOrganization = orgData[0];
			  if (defaultOrganization) {
				setDefaultOrganization(defaultOrganization);
				setOrganizationId(defaultOrganization.OrganizationId);
				setOrgName(defaultOrganization.name); // Set the default name
			}
		  }
		};
	  
		fetchAndSetOrganizations();
	  }, []);
	  
	useEffect(() => {
		const fetchAndSetOrganizations = async () => {
		  const orgData = await fetchOrganizations(setOrganizations, setLoading);
		  if (orgData) {
			setOrganizations(orgData);
			  // Find "Quixy" and set it as the default selected organization
			const defaultOrganization = orgData.find((org:any) => org.Name === "Quixy");
			if (defaultOrganization) {
			  setOrganization(defaultOrganization.OrganizationId);
			}
		  }
		};
	  
		fetchAndSetOrganizations();
	  }, []);
	  
	// useEffect(() => {
	// 	const fetchAndSetOrganizations = async () => {
	// 	  const orgData = await fetchOrganizations(setOrganizations, setLoading);
	// 		if (orgData) {
	// 		setOrganizations(orgData);
	// 	  }
	// 	};
	
	// 	fetchAndSetOrganizations();
	// }, []);
// here from selected or default orgid setting users 
	useEffect(() => {
		if (organizationId) {
		  const fetchAndSetUsers = async () => {
			await fetchUsersList(
			  setModels, // Function to set models
			  setLoading, // Function to set loading state
			  organizationId, // Organization ID
			  0, // Initial skip
			  15, // Limit for top
			  setTotalcount, // Function to set total count
			  '', // Default ordering field
			  [] // Default filters
			);
		  };
	  
			fetchAndSetUsers();
			fetchFilteredData(selectedUser,organizationId);
			setOrganizationId(organizationId);
		}
	  }, [organizationId]); 
	  useEffect(() => {
		if (Array.isArray(models)) {
	  
		  const usernames = models.map(user => user.EmailId); 
		  setUsers(usernames);
		} else {
		  console.error('Models is not an array:', models);
		}
	  }, [models]); 
	  const today: Dayjs = dayjs(); // Always use Dayjs
	  const defaultFromDate: Dayjs = today.subtract(2, 'day');
	  const [fromDate, setFromDate] = useState<Dayjs | null>(dayjs(defaultFromDate));
	  const [toDate, setToDate] = useState<Dayjs | null>(dayjs(today).endOf('day'));
	  
// useEffect(() => {
// 	// Set default dates to last 3 days
// 	const today = new Date();
// 	const defaultFromDate = new Date(today);
// 	defaultFromDate.setDate(today.getDate() - 3);
  
// 	setFromDate(defaultFromDate);
// 	setToDate(today);
//   }, []); // Empty dependency array to run only once on component mount
  
  useEffect(() => {
	  fetchFilteredData(selectedUser, organizationId);
  }, [paginationModel]); 

  
//   useEffect(() => {
// 	if (fromDate && toDate) {
// 	  fetchFilteredData(selectedUser);
// 	}
	//   },  [selectedUser, fromDate, toDate, paginationModel.page, paginationModel.pageSize, type]);
	
	const fetchFilteredData = async (selectedUser:any,organizationId: any) => {
		if (!organizationId) {
		  console.error("Organization ID is required to fetch data");
		  return;
		}
	  
		try {
		  // Set loading to true before the request
		  setLoading(true);
	  
		  // Calculate skips and tops based on pagination model
		  const skipcount = paginationModel.pageSize || 15;
		  const limitcount = paginationModel.page * skipcount;
		  const skips = limitcount;
		  const tops = paginationModel.pageSize;
	  
		  // Set skip and top state
		  setskip(skips);
		  settop(tops);
	  
		  // Fetch audit logs with the adjusted function call
		  const response = await GetAuditLogsBySearch(
			skips,
			tops,
			organizationId,
			orderByFields,
			filters,
			fromDate?.toISOString(),
			toDate?.toISOString(),
			setTotalcount,
			  setAuditLogs,
			  type,
			  referenceType,
			  nameFilter,
			selectedUser
		  );
	  
		  if (response && Array.isArray(response)) {
			// Process the response data
			const auditLogData: AuditLog[] = response.map((log: any) => ({
			  AuditLogId: log.AuditLogId,
			  ReferenceId: log.ReferenceId,
			  Name: log.Name,
			  OrganizationId: log.OrganizationId,
			  ReferenceType: log.ReferenceType,
			  Type: log.Type,
			  CreatedBy: log.CreatedBy,
			  Browser: log.Browser,
			  IPAddress: log.IPAddress,
			  CreatedDate: formatIndianDateTime(log.CreatedDate),
			}));
	  
			setAuditLogs(auditLogData);
		  } else {
			console.error("Invalid response format:", response);
		  }
		} catch (error) {
		  console.error('Error fetching audit logs:', error);
		} finally {
		  setLoading(false);
		}
	};
	const handleSearch = async (organizationId: any) => {
		if (!organizationId) {
		  console.error("Organization ID is required to perform search");
		  return;
		}
	  
		try {
		  // Set loading state to true before fetching data
		  setLoading(true);
	  
		  // Calculate skips and tops based on pagination model
		  const skipcount = paginationModel.pageSize || 15;
		  const limitcount = paginationModel.page * skipcount;
		  const skips = limitcount;
		  const tops = paginationModel.pageSize;
	  
		  // Set skip and top state
		  setskip(skips);
		  settop(tops);
	  
		  // Fetch audit logs using the GetAuditLogsBySearch function
		  const response = await GetAuditLogsBySearch(
			skips,
			tops,
			organizationId,
			orderByFields,
			filters,
			fromDate?.toISOString(),
			toDate?.toISOString(),
			setTotalcount,
			  setAuditLogs,
			  type,
			  referenceType,
			  name,
			createdUser
		  );
	  
		  if (response && Array.isArray(response)) {
			// Process the response data
			const auditLogData: AuditLog[] = response.map((log: any) => ({
			  AuditLogId: log.AuditLogId,
			  ReferenceId: log.ReferenceId,
			  Name: log.Name,
			  OrganizationId: log.OrganizationId,
			  ReferenceType: log.ReferenceType,
			  Type: log.Type,
			  CreatedBy: log.CreatedBy,
			  Browser: log.Browser,
			  IPAddress: log.IPAddress,
			  CreatedDate: formatIndianDateTime(log.CreatedDate),
			}));
	  
			setAuditLogs(auditLogData);
		  } else {
			console.error("Invalid response format:", response);
		  }
		} catch (error) {
		  console.error("Error fetching audit logs:", error);
		} finally {
		  setLoading(false);
		}
	  };
  
//   const handleSearch = async (organizationId:any) => {
//     // try {
// 	// 	setLoading(true);
// 	// 	const params: SearchParams = {
// 	// 	  skip: paginationModel.pageSize * paginationModel.page,
// 	// 	  top: paginationModel.pageSize,
// 	// 	  fromDate: fromDate ? fromDate.toISOString() : undefined,
// 	// 	  toDate: toDate ? toDate.toISOString() : undefined,
// 	// 		createdUser: selectedUser || undefined,
// 	// 	  name: nameFilter || undefined,
// 	// 		type: type || undefined,
// 	// 		referenceType:referenceType|| undefined,
// 	// 	  organizationId:organizationId||undefined
// 	// 	};
	
// 	// 	  const response = await GetAuditLogsBySearch(params, setTotalcount);
// 	// 	//let allResults: AuditLog[] = [...allAuditLogData];
// 	// 	const auditLogData: AuditLog[] = response.results.map((log: any) => ({
// 	// 	  AuditLogId: log.AuditLogId,
// 	// 	  ReferenceId: log.ReferenceId,
// 	// 		Name: log.Name,
// 	// 	  OrganizationId: log.OrganizationId,
// 	// 	  ReferenceType: log.ReferenceType,
// 	// 		Type: log.Type,
// 	// 		CreatedBy: log.CreatedBy,
// 	// 		Browser: log.Browser,
// 	// 		IPAddress: log.IPAddress,
// 	// 		CreatedDate: formatIndianDateTime(log.CreatedDate),
  
// 	// 	}));
	
// 	// 	// allResults = [...allResults, ...auditLogData];
// 	// 	// setAllAuditLogData(allResults);
// 	// 	setAuditLogs(auditLogData);
//     // } catch (error) {
//     //     console.error("Error fetching audit logs:", error);
//     // } finally {
//     //     setLoading(false);
//     // }
// };




	const handleDownloadExcel = async () => {
		// Implement the download Excel functionality here
	};

	const CustomToolbar: React.FC<any> = () => {
		const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

		const handleExportMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {
			setAnchorEl(event.currentTarget);
		};

		const handleExportMenuClose = () => {
			setAnchorEl(null);
		};

		return (
			<GridToolbarContainer>
				<GridToolbarColumnsButton />
				<GridToolbarFilterButton />
				<GridToolbarDensitySelector />
				<Button
					aria-controls="export-menu"
					aria-haspopup="true"
					onClick={handleExportMenuClick}
					style={{ marginLeft: "10px" }}
					startIcon={<SaveAltIcon />}
				>
					Export
				</Button>
				<Menu
					id="export-menu"
					anchorEl={anchorEl}
					keepMounted
					open={Boolean(anchorEl)}
					onClose={handleExportMenuClose}
				>
					<MenuItem
						onClick={() => {
							handleDownloadExcel();
							handleExportMenuClose();
						}}
					>
						Download Excel
					</MenuItem>
				</Menu>
			</GridToolbarContainer>
		);
	};

	useEffect(() => {
		subscribe(() => {
			setSidebarOpen(isSidebarOpen());
		});
	}, []);


	return (
		<div>

<div className="qadpt-head">
						<div className="qadpt-title-sec">
							<div className="qadpt-title">Audit Logs</div>
						</div>
						<div className="qadpt-right-part">
						
				<Autocomplete
					options={organizations}
					getOptionLabel={(option) => option.Name}
					value={organizationId ? organizations.find(org => org.OrganizationId === organizationId) : null}
					onChange={(event, newValue) => {
						setOrganizationId(newValue?.OrganizationId || null);
					  }}
						
					renderInput={(params) => (
						<TextField
							{...params}
							label="Select Organization"
							InputProps={{
								...params.InputProps,
								sx: {
									'& .MuiAutocomplete-popupIndicator': {
										top: '-1px'
									},
									'& .MuiAutocomplete-clearIndicator': {
										visibility: referenceType ? 'visible' : 'hidden',
										top: '-1px'
									},
								}
							}}
						/>
					)}
					
					// sx={{
					// 	width: 195,
					// 	marginRight: 2,
					// 	marginLeft: "-30px",
					// 	position: "relative", // Ensure the element can be positioned
					// 	top: "-20px", 
					// 	'& .MuiOutlinedInput-input': {
					// 		padding: '0px',
					// 		height: '18px',
					// 		top: "-11px"
					// 	},
					// 	'& .MuiOutlinedInput-notchedOutline': {
					// 		borderRadius: "5px",
					// 		height: "35px",

					// 	},
					// 	'& .MuiInputLabel-outlined': {
					// 		top: "-9px"
					// 	},
					// 	'& .MuiAutocomplete-inputRoot': {
					// 					height: "35px"
					// 				}

					// }}
				/>
			
			</div>
					</div>
			
		
  {/* <p className="org-text">Organization</p> */}
				{/* <p className="org-text">Organization</p> */}
			
		

			
   <div className="grid-toolbar-options">
				<div className="left-options">
         <div className="drp-fields">
            <Autocomplete className="auto-filed"
               options={Object.keys(categories)}
               value={referenceType}
               onChange={(event, newValue) =>
                  setReferenceType(newValue)}
               renderInput={(params) => (
                  <TextField className={`qadpt-activty-category ${referenceType ? 'visible' : 'hidden'}`}
                     {...params}
                     label= "Category"
                     InputProps={{
                        ...params.InputProps,
                     }}
                  />
               )} />
            <Autocomplete className="auto-filed"
               options={typeOptions}
               value={type}
               onChange={(event, newValue) =>
                  setType(newValue)}
               renderInput={(params) => (
                  <TextField className={`qadpt-activty-category ${referenceType ? 'visible' : 'hidden'}`}
                     {...params}
                     label="Event Type"
                     InputProps={{
                        ...params.InputProps,
                     }}
                  />
               )} />
         </div>
         <div className="dt-fields">
			<LocalizationProvider dateAdapter={AdapterDayjs}>
			<DateTimePicker className={`qadpt-DateTime ${fromDate ? '' : 'hide-close'}`}
								label="From Date"
								views={['year', 'month', 'day']}
				value={fromDate}
				onChange={(newValue) =>
					setFromDate(newValue ? dayjs(newValue) : null)}
				maxDateTime={toDate ?? undefined}
				
				slotProps={{
					textField: {
						className: "qadpt-datepicker", 
					},
					field: { clearable: true },
				}}
               />
               <DateTimePicker className={`qadpt-DateTime dt-fld2 ${fromDate ? '' : 'hide-close'}`}
								label="To Date"
								views={['year', 'month', 'day']}
                  value={toDate}
                  onChange={(newValue) =>
                     setToDate(newValue ? dayjs(newValue).endOf('day') : null)}
                  minDateTime={fromDate ?? undefined}
				slotProps={{
					textField: {
					  className: "qadpt-datepicker", 
					},
					field: { clearable: true },
				}}
               />
            </LocalizationProvider>
         </div>
         <TextField className="name-fld"
            variant="outlined"
            label="Name"
            value={name}
            onChange={(e) => {
               setName(e.target.value);
               setNameFilter(e.target.value); // Update nameFilter when name changes
            }}
					/>
				 <div className="right-options">
         <Button
            variant="outlined"
            color="primary"
            onClick={() => {
               // Immediately clear the audit logs data
               // setAuditLogs([]); 
               // Set clearing mode to true
               setIsClearing(true);
               // Reset state and data
               setEnvironment("");
               setType("");
               setReferenceType("");
               setName("");
               setNameFilter("");
               setFromDate(null);
               setToDate(null);
               setUser("");
               // Reset pagination without triggering data fetch
               setPaginationModel({ page: 0, pageSize: 15 });
            }}>
            Clear
         </Button>
         <Button
            variant="contained"
            color="primary"
            onClick={() => handleSearch(organizationId)}
            disabled={environment === "" &&
               (type === "" || type === null) &&
               (referenceType === "" || referenceType === null) &&
               fromDate === null &&
               toDate === null &&
               user === "" && name === ""}>
           Search
         </Button>
      </div>	
      </div>
     
   </div>

			{loading ? (
				<div className="Loaderstyles">
					<img
						src={loader}
						alt="Spinner"
						className="LoaderSpinnerStyles"
					/>
				</div>
			) : (
						<DataGrid
							 className="qadpt-setting-grd"
							// sx={{
							// 	borderColor: "#F6EEEE",
							// 	"& .MuiDataGrid-columnHeaders": {
							// 		backgroundImage: "linear-gradient(to right, #F6EEEE, #F6EEEE)",
							// 	},
							// 	"& .MuiDataGrid-columnHeaderTitle": {
							// 		fontWeight: "bold",
							// 		color: "#767475",
							// 	},
							// 	"& .MuiDataGrid-columnHeader": {
							// 		backgroundImage: "linear-gradient(to right, #F6EEEE, #F6EEEE)",
							// 		color: "black",
							// 	},
							// 	"& .MuiDataGrid-cell": {
							// 		borderRight: "1px solid #F6EEEE",
							// 	},
							// 	"& .MuiDataGrid-columnHeader, .MuiDataGrid-cell": {
							// 		borderRight: "1px solid #F6EEEE",
							// 	},
							// 	"& .MuiDataGrid-row": {
							// 		"& .MuiDataGrid-cell": {
							// 			borderBottom: "none",
							// 		},
							// 	},
							// 	position: "relative",
							// }}
							//   sx={{
							// 	borderColor: "black",
							// 	"& .MuiDataGrid-columnHeaders": {
							// 	  backgroundImage: "linear-gradient(to right, rgb(30, 138, 201), rgba(62, 200, 241, 0.7))",
							// 	},
							// 	"& .MuiDataGrid-columnHeaderTitle": {
							// 	  fontWeight: "bold",
							// 	  color: "white",
							// 	},
							// 	"& .MuiDataGrid-columnHeader": {
							// 	  backgroundImage: "linear-gradient(to right, rgb(30, 138, 201), rgba(62, 200, 241, 0.7))",
							// 	  borderBottom: "2px solid #ddd", // Increased border weight for column headers
							// 	  color: "white",
							// 	},
							// 	"& .MuiDataGrid-columnHeader--alignLeft": {
							// 	  backgroundImage: "linear-gradient(to right, rgb(30, 138, 201), rgba(62, 200, 241, 0.7))",
							// 	  color: "white",
							// 	},
							// 	"& .MuiDataGrid-cell": {
							// 	  borderBottom: "2px solid #ddd", // Increased border weight for cells
							// 	  borderRight: "2px solid #ddd", // Increased border weight for cells
							// 	},
							// 	"& .MuiDataGrid-columnHeader, .MuiDataGrid-cell": {
							// 	  borderRight: "2px solid #ddd", // Increased border weight for both headers and cells
							// 	},
							// 	"& .MuiDataGrid-row": {
							// 	  "&:last-child .MuiDataGrid-cell": {
							// 		borderBottom: "none", // Remove border from the last row
							// 	  },
							// 	},
							//   }}
                            rows={auditLogs}
							columns={columns}
							getRowId={(row) => row.AuditLogId}

							paginationModel={paginationModel}
							onPaginationModelChange={setPaginationModel}
													  pagination
													  paginationMode="server"
							rowCount={totalcount}
							pageSizeOptions={[15, 25, 50, 100]}
							localeText={{
							  MuiTablePagination: {
								labelRowsPerPage: "Records Per Page",
							  },
							}}
							disableRowSelectionOnClick={true}
							loading={loading}
/>

			)}
		</div>
		
	);
	
};

export default SuperAdminAuditLogList;
