import React, { useState, useEffect } from 'react';
import {
    Box,
    Button,
    Container,
    TextField,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    IconButton,
    Tooltip,
    Alert
} from '@mui/material';
import { DataGrid, GridColDef, GridPaginationModel } from '@mui/x-data-grid';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../auth/AuthProvider';
import { isSidebarOpen, subscribe } from "../adminMenu/sidemenustate";
import { useContext } from 'react';
import { AccountContext } from '../account/AccountContext';
import { useSnackbar } from '../../SnackbarContext';
import DeleteIcon from '@mui/icons-material/Delete';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import {
    getAllTrainingDocuments,
    uploadTrainingDocument,
    DeleteKnowledgeBase,
    updateTrainingDocument,
    downloadTrainingDocument,
    replaceTrainingDocumentFile
} from '../../services/TrainingService';
import { TrainingDocument, DocumentTypes, PriorityLevels } from '../../models/Training';

interface TrainingProps {}

const Training: React.FC<TrainingProps> = () => {
    const { t: translate } = useTranslation();
    const { userDetails } = useAuth();
    const { accountId } = useContext(AccountContext);
    const { openSnackbar } = useSnackbar();
    const [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());
    const [models, setModels] = useState<TrainingDocument[]>([]);
    const [loading, setLoading] = useState(false);
    const [totalCount, setTotalCount] = useState(0);
    const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({
        page: 0,
        pageSize: 15,
    });
    const [dialogOpen, setDialogOpen] = useState(false);
    const [editMode, setEditMode] = useState(false);
    const [selectedDocument, setSelectedDocument] = useState<TrainingDocument | null>(null);
    const [uploadFile, setUploadFile] = useState<File | null>(null);
    const [replaceFile, setReplaceFile] = useState<File | null>(null);
    const [formData, setFormData] = useState({
        title: '',
        documentType: '',
        priority: 1
    });
    const [filters, setFilters] = useState<any[]>([]);
    const [orderByFields, setOrderByFields] = useState('CreatedDate desc');

    useEffect(() => {
        const unsubscribe = subscribe(setSidebarOpen);
        return () => unsubscribe();
    }, []);

    useEffect(() => {
        if (userDetails?.OrganizationId) {
            fetchTrainingDocuments();
        }
    }, [userDetails?.OrganizationId, paginationModel, accountId]);

    const fetchTrainingDocuments = async () => {
        if (!userDetails?.OrganizationId) return;

        const skip = paginationModel.page * paginationModel.pageSize;
        const top = paginationModel.pageSize;

        await getAllTrainingDocuments(
            setModels,
            setLoading,
            userDetails.OrganizationId,
            skip,
            top,
            setTotalCount,
            orderByFields,
            filters,
            accountId
        );
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            setUploadFile(file);
        }
    };

    const handleReplaceFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            setReplaceFile(file);
        }
    };

    const handleFormChange = (field: string, value: any) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleUpload = async () => {
        if (!uploadFile || !formData.title || !formData.documentType || !userDetails?.OrganizationId || !accountId) {
            openSnackbar('Please fill all required fields and select a file', 'error');
            return;
        }

        try {
            await uploadTrainingDocument(
                uploadFile,
                formData.title,
                formData.documentType,
                formData.priority,
                userDetails.OrganizationId,
                userDetails.UserId || '',
                accountId
            );

            openSnackbar('Document uploaded successfully', 'success');
            setDialogOpen(false);
            resetForm();
            fetchTrainingDocuments();
        } catch (error) {
            openSnackbar('Failed to upload document', 'error');
        }
    };

    const handleEdit = (document: TrainingDocument) => {
        setSelectedDocument(document);

        // Debug: Log the document type to understand the format
        console.log('Document Type from API:', document.DocumentType);
        console.log('Available Document Types:', DocumentTypes.map(dt => dt.value));

        // Map the document type to the correct form value
        const documentTypeValue = mapDocumentTypeToFormValue(document.DocumentType);

        console.log('Mapped Document Type:', documentTypeValue);

        // Show warning if document type couldn't be mapped to a known type
        if (document.DocumentType && !DocumentTypes.some(dt => dt.value === documentTypeValue)) {
            console.warn(`Document type "${document.DocumentType}" doesn't match any predefined types. Using original value.`);
        }

        setFormData({
            title: document.Title,
            documentType: documentTypeValue,
            priority: document.Priority
        });
        setEditMode(true);
        setDialogOpen(true);
    };

    const handleUpdate = async () => {
        if (!selectedDocument || !formData.title || !formData.documentType || !userDetails?.OrganizationId || !accountId) {
            openSnackbar('Please fill all required fields', 'error');
            return;
        }

        try {
            // If a replacement file is selected, use the file replacement API
            if (replaceFile) {
                await replaceTrainingDocumentFile(
                    selectedDocument.Id,
                    replaceFile,
                    formData.title,
                    formData.documentType,
                    formData.priority,
                    userDetails.OrganizationId,
                    userDetails.UserId || '',
                    accountId
                );

            } else {
                // Otherwise, just update the metadata
                await updateTrainingDocument(
                    selectedDocument.Id,
                    formData.title,
                    formData.documentType,
                    formData.priority,
                    userDetails.OrganizationId,
                    userDetails.UserId || ''
                );

                setDialogOpen(false);
                resetForm();
                fetchTrainingDocuments();
            }
        } catch (error) {
            openSnackbar('Failed to update document', 'error');
        }
    };

    const handleDelete = async (documentId: string) => {
        if (!userDetails?.OrganizationId) return;

        if (window.confirm('Are you sure you want to delete this document?')) {
            try {
                await DeleteKnowledgeBase(documentId);
                openSnackbar('Document deleted successfully', 'success');
                fetchTrainingDocuments();
            } catch (error) {
                openSnackbar('Failed to delete document', 'error');
            }
        }
    };

    const handleDownload = async (documentId: string) => {
        if (!userDetails?.OrganizationId) return;

        try {
            await downloadTrainingDocument(documentId, userDetails.OrganizationId);
        } catch (error) {
            openSnackbar('Failed to download document', 'error');
        }
    };

    const resetForm = () => {
        setFormData({
            title: '',
            documentType: '',
            priority: 1
        });
        setUploadFile(null);
        setReplaceFile(null);
        setSelectedDocument(null);
        setEditMode(false);
    };

    // Validate if the current document type is valid
    const isValidDocumentType = (docType: string): boolean => {
        return DocumentTypes.some(dt => dt.value === docType) || docType === '';
    };

    const openUploadDialog = () => {
        resetForm();
        setDialogOpen(true);
    };

    const getPriorityLabel = (priority: number) => {
        const priorityLevel = PriorityLevels.find(p => p.value === priority);
        return priorityLevel ? priorityLevel.label : 'Unknown';
    };

    const getPriorityColor = (priority: number): "default" | "primary" | "secondary" | "error" | "info" | "success" | "warning" => {
        switch (priority) {
            case 1: return 'info';
            case 2: return 'primary';
            case 3: return 'warning';
            case 4: return 'error';
            default: return 'default';
        }
    };

    const getDocumentTypeLabel = (type: string) => {
        const docType = DocumentTypes.find(dt => dt.value === type);
        return docType ? docType.label : type;
    };

    // Utility function to map API document type to form value
    const mapDocumentTypeToFormValue = (apiDocumentType: string): string => {
        if (!apiDocumentType) return '';

        // Try exact match first
        const exactMatch = DocumentTypes.find(dt => dt.value === apiDocumentType);
        if (exactMatch) return exactMatch.value;

        // Try case-insensitive match on value
        const valueMatch = DocumentTypes.find(dt =>
            dt.value.toLowerCase() === apiDocumentType.toLowerCase()
        );
        if (valueMatch) return valueMatch.value;

        // Try case-insensitive match on label
        const labelMatch = DocumentTypes.find(dt =>
            dt.label.toLowerCase() === apiDocumentType.toLowerCase()
        );
        if (labelMatch) return labelMatch.value;

        // Return original value if no match found
        return apiDocumentType;
    };

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const columns: GridColDef[] = [
        {
            field: 'Name',
            headerName: translate('File Name'),
            // width: 300,
            resizable: false,
            sortable: true
        },
        {
            field: 'Title',
            headerName: translate('Title'),
            // width: 200,
            resizable: false,
            sortable: true
        },
        {
            field: 'DocumentType',
            headerName: translate('Document Type'),
            // width: 180,
            sortable: true,
            resizable: false,
            renderCell: (params) => getDocumentTypeLabel(params.value)
        },
        {
            field: 'EmbeddingStatus',
            headerName: translate('Training Status'),
            // width: 180,
            sortable: true,
            resizable: false,
            renderCell: (params) => getDocumentTypeLabel(params.value)
        },

        // {
        //     field: 'Priority',
        //     headerName: translate('Priority'),
        //     width: 120,
        //     sortable: true,
        //     renderCell: (params) => (
        //         <Chip
        //             label={getPriorityLabel(params.value)}
        //             color={getPriorityColor(params.value)}
        //             size="small"
        //         />
        //     )
        // },
        // {
        //     field: 'FileSize',
        //     headerName: translate('File Size'),
        //     width: 120,
        //     sortable: true,
        //     renderCell: (params) => formatFileSize(params.value || 0)
        // },
        {
            field: 'CreatedDate',
            resizable: false,
            headerName: translate('Upload Date'),
            // width: 150,
            sortable: true,
            renderCell: (params) => new Date(params.value).toLocaleDateString()
        },
        {
            field: 'actions',
            headerName: translate('Actions'),
            // width: 150,
            sortable: false,
            resizable: false,
            renderCell: (params) => (
                <div>
                    {/* <Tooltip title="Download">
                        <IconButton
                            size="small"
                            onClick={() => handleDownload(params.row.Id)}
                        >
                            <DownloadIcon />
                        </IconButton>
                    </Tooltip> */}
                    {/* <Tooltip title="Edit">
                        <IconButton
                            size="small"
                            onClick={() => handleEdit(params.row)}
                        >
                            <EditIcon />
                        </IconButton>
                    </Tooltip> */}
                    <Tooltip title="Delete">
                        <IconButton
                            size="small"
                            onClick={() => handleDelete(params.row.Id)}
                        >
                            <DeleteIcon />
                        </IconButton>
                    </Tooltip>
                </div>
            )
        }
    ];

    return (
        <Container>
            <div className="qadpt-midpart setng-box">
                <div className="qadpt-content-block">
                    <div className="qadpt-head">
                        <div className="qadpt-title-sec">
                            <div className="qadpt-title">{translate('Training')}</div>
                            <div className="qadpt-description">{translate('Manage knowledge base documents')}</div>
                        </div>
                        <div className="qadpt-right-part">
                            <button
                                onClick={openUploadDialog}
                                className="qadpt-memberButton"
                            >
                                <i className="fal fa-add-plus"></i>
                                <span>{translate('Upload Document')}</span>
                            </button>
                        </div>
                    </div>

                    <div style={{marginTop:"30px"}}>
                        <DataGrid
                            className="qadpt-setting-grd"

                            rows={models}
                            columns={columns}
                            pagination
                            paginationMode="server"
                            rowCount={totalCount}
                            paginationModel={paginationModel}
                            onPaginationModelChange={setPaginationModel}
                            loading={loading}
                            pageSizeOptions={[15, 25, 50]}
                            getRowId={(row) => row.Id}
                            disableRowSelectionOnClick
                        />
                    </div>
                </div>
            </div>

            <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth className='qadpt-trainpop'>
                <DialogTitle>
                    {editMode ? translate('Edit Document') : translate('Upload Document')}
                </DialogTitle>
                <DialogContent>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }} className="qadpt-uplddoc">
                        {!editMode && (
                            <Button
                                variant="outlined"
                                component="label"
                                startIcon={<CloudUploadIcon />}
                                fullWidth
                            >
                                {uploadFile ? uploadFile.name : translate('Select File')}
                                <input
                                    type="file"
                                    hidden
                                    onChange={handleFileChange}
                                    accept=".pdf,.doc,.docx,.txt,.md"
                                />
                            </Button>
                        )}

                        {editMode && (
                            <Box>
                                {/* Show current file information */}
                                <Box sx={{ mb: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>

                                    </Box>
                                    {selectedDocument?.FileSize && (
                                        <Box sx={{ color: 'text.secondary', fontSize: '0.875rem' }}>
                                            {translate('Size')}: {formatFileSize(selectedDocument.FileSize)}
                                        </Box>
                                    )}
                                </Box>

                                {/* File replacement option */}
                                <Button
                                    variant="outlined"
                                    component="label"
                                    startIcon={<CloudUploadIcon />}
                                    fullWidth
                                    color={replaceFile ? "success" : "primary"}
                                >
                                    {replaceFile ? `${translate('New File Selected')}: ${replaceFile.name}` : translate('Change File (Optional)')}
                                    <input
                                        type="file"
                                        hidden
                                        onChange={handleReplaceFileChange}
                                        accept=".pdf,.doc,.docx,.txt,.md"
                                    />
                                </Button>

                                {replaceFile && (
                                    <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                                        <Alert severity="info" sx={{ flexGrow: 1 }}>
                                            {translate('The current file will be replaced with the new file')}
                                        </Alert>
                                        <Button
                                            size="small"
                                            onClick={() => setReplaceFile(null)}
                                            color="error"
                                            variant="outlined"
                                        >
                                            {translate('Keep Current File')}
                                        </Button>
                                    </Box>
                                )}
                            </Box>
                        )}

                        <TextField
                            label={translate('Title')}
                            value={formData.title}
                            onChange={(e) => handleFormChange('title', e.target.value)}
                            fullWidth
                            required
                        />

                        <FormControl fullWidth required>
                            <InputLabel>{translate('Document Type')}</InputLabel>
                            <Select
                                value={formData.documentType || ''}
                                onChange={(e) => handleFormChange('documentType', e.target.value)}
                                label={translate('Document Type')}
                                displayEmpty={false}
                            >
                                {DocumentTypes.map((type) => (
                                    <MenuItem key={type.value} value={type.value}>
                                        {type.label}
                                    </MenuItem>
                                ))}
                                {/* Add fallback option if current value doesn't match any predefined types */}
                                {formData.documentType &&
                                 !DocumentTypes.some(dt => dt.value === formData.documentType) && (
                                    <MenuItem key={formData.documentType} value={formData.documentType}>
                                        {formData.documentType} (Custom)
                                    </MenuItem>
                                )}
                            </Select>
                        </FormControl>

                        <FormControl fullWidth>
                            <InputLabel>{translate('Priority')}</InputLabel>
                            <Select
                                value={formData.priority}
                                onChange={(e) => handleFormChange('priority', e.target.value)}
                                label={translate('Priority')}
                            >
                                {PriorityLevels.map((level) => (
                                    <MenuItem key={level.value} value={level.value}>
                                        {level.label}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Box>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setDialogOpen(false)}>
                        {translate('Cancel')}
                    </Button>
                    <Button
                        onClick={editMode ? handleUpdate : handleUpload}
                        variant="contained"
                    >
                        {editMode ? translate('Update') : translate('Upload')}
                    </Button>
                </DialogActions>
            </Dialog>
        </Container>
    );
};

export default Training;
