import React, { useEffect, useState } from 'react';
import { Navigate, useNavigate } from 'react-router-dom';
import userManager from '../components/auth/UseAuth';
import Login from "../components/login/login";
import jwt_decode from "jwt-decode";
import { LoginUserInfo } from '../models/LoginUserInfo';
import { Home } from '@mui/icons-material';
import { useAuth } from '../components/auth/AuthProvider';

const Callback: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  useEffect(() => {
    userManager.signinRedirectCallback().then((user) => {
      setIsLoggedIn(true);  
      navigate("/")
    }).catch((err) => {
      userManager.signoutRedirect();
    });
  }, []);
  
  if (isLoggedIn) {    
    return <Home/>;  
  }
  
  
  return <div>Loading...</div>;
 
};

export default Callback;
