import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface ExtensionContextType {
  isExtensionInstalled: boolean;
  checkingExtension: boolean;
  checkExtensionStatus: () => void;
}

const ExtensionContext = createContext<ExtensionContextType | undefined>(undefined);

interface ExtensionProviderProps {
  children: ReactNode;
}

export const ExtensionProvider: React.FC<ExtensionProviderProps> = ({ children }) => {
  const [isExtensionInstalled, setIsExtensionInstalled] = useState<boolean>(false);
  const [checkingExtension, setCheckingExtension] = useState<boolean>(true);

  // Function to check for extension marker
  const checkExtensionMarker = () => {
    // Check for marker element
    const markerExists = document.getElementById('quickadapt-extension-marker');
    // Check for data attribute
    const bodyHasAttribute = document.body.getAttribute('data-quickadapt-extension-installed');
    // Check for global flag
    const globalFlag = (window as any).quickadaptExtensionInstalled;

    if (markerExists || bodyHasAttribute === 'true' || globalFlag) {
      setIsExtensionInstalled(true);
      return true;
    }
    setIsExtensionInstalled(false);
    return false;
  };

  // Function to check extension status on demand
  const checkExtensionStatus = () => {
    setCheckingExtension(true);
    const isInstalled = checkExtensionMarker();
    setCheckingExtension(false);
    return isInstalled;
  };

  useEffect(() => {

    // Setup message listener for extension communication
    const handleExtensionMessage = (event: MessageEvent) => {
      if (event.data && event.data.type === 'QUICKADAPT_EXTENSION_INSTALLED') {
        setIsExtensionInstalled(true);
        setCheckingExtension(false);
      }
    };

    window.addEventListener('message', handleExtensionMessage);

    // Check for extension immediately
    if (checkExtensionMarker()) {
      setCheckingExtension(false);
      return;
    }

    // Poll for extension marker
    let attempts = 0;
    const maxAttempts = 20;
    const intervalMs = 100;

    const interval = setInterval(() => {
      if (checkExtensionMarker()) {
        clearInterval(interval);
        setCheckingExtension(false);
        console.log("✅ QuickAdapt Extension IS installed.");
      } else {
        attempts++;
        if (attempts >= maxAttempts) {
          console.log("❌ QuickAdapt Extension is NOT installed.");
          setCheckingExtension(false);
          clearInterval(interval);
        }
      }
    }, intervalMs);

    return () => {
      clearInterval(interval);
      window.removeEventListener('message', handleExtensionMessage);
    };
  }, []);

  return (
    <ExtensionContext.Provider value={{ isExtensionInstalled, checkingExtension, checkExtensionStatus }}>
      {children}
    </ExtensionContext.Provider>
  );
};

export const useExtension = (): ExtensionContextType => {
  const context = useContext(ExtensionContext);
  if (!context) {
    throw new Error('useExtension must be used within an ExtensionProvider');
  }
  return context;
};
