  
import React from 'react';
import GuideTable from "./GuideTable"

const guidesList = () => {
  return (
		<div className="guides">
			<span className="guides-text">Guides</span>
			<div className="guides-table">
				<div className="search-panel">
					<div className="search-block">
						<label className="search-label">
							<svg
								stroke="currentColor"
								fill="currentColor"
								stroke-width="0"
								viewBox="0 0 24 24"
								color="#919DB5"
								height="1em"
								width="1em"
								xmlns="http://www.w3.org/2000/svg"
								style={{ color: " rgb(145, 157, 181)" }}
							>
								<path d="M11 2C15.968 2 20 6.032 20 11C20 15.968 15.968 20 11 20C6.032 20 2 15.968 2 11C2 6.032 6.032 2 11 2ZM11 18C14.8675 18 18 14.8675 18 11C18 7.1325 14.8675 4 11 4C7.1325 4 4 7.1325 4 11C4 14.8675 7.1325 18 11 18ZM19.4853 18.0711L22.3137 20.8995L20.8995 22.3137L18.0711 19.4853L19.4853 18.0711Z"></path>
							</svg>
							<input
								className="search-input"
								type="text"
								placeholder="Search"
								value=""
							></input>
						</label>
						<div className="search-filter">
							<button
								id="filter-button"
								className="filter-button"
							>
								<svg
									stroke="currentColor"
									fill="currentColor"
									stroke-width="0"
									viewBox="0 0 24 24"
									height="17"
									width="17"
									xmlns="http://www.w3.org/2000/svg"
								>
									<path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path>
								</svg>
								<span className="filter-span">Filter</span>
							</button>
						</div>
						<button className="newguide-button">
							<svg
								stroke="currentColor"
								fill="currentColor"
								stroke-width="0"
								viewBox="0 0 24 24"
								height="17"
								width="17"
								xmlns="http://www.w3.org/2000/svg"
							>
								<path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path>
							</svg>
							<span className="sc-cwHptR lnyVkX">New Guide</span>
						</button>
					</div>
				</div>
				<div className="select-box">
					<div className="sc-dAlyuH select-svg">
						<label
							data-testid="checkbox-container-test-id"
							className="sc-dAbbOL jvijPe"
						>
							<div>
								<input type="checkbox" />
								<div></div>
							</div>
							<div>
								<span>Select All</span>
							</div>
						</label>
					</div>
					<div className="select-panel">
						<button
							id="clone-button"
							className="select-button"
						>
							<div className="sc-dAlyuH select-svg">
								<svg
									stroke="currentColor"
									fill="currentColor"
									stroke-width="0"
									viewBox="0 0 24 24"
									height="20"
									width="20"
									xmlns="http://www.w3.org/2000/svg"
								>
									<path d="M6.99979 7V3C6.99979 2.44772 7.4475 2 7.99979 2H20.9998C21.5521 2 21.9998 2.44772 21.9998 3V16C21.9998 16.5523 21.5521 17 20.9998 17H17V20.9925C17 21.5489 16.551 22 15.9925 22H3.00728C2.45086 22 2 21.5511 2 20.9925L2.00276 8.00748C2.00288 7.45107 2.4518 7 3.01025 7H6.99979ZM8.99979 7H15.9927C16.549 7 17 7.44892 17 8.00748V15H19.9998V4H8.99979V7ZM4.00255 9L4.00021 20H15V9H4.00255Z"></path>
								</svg>
							</div>
						</button>
						<button
							id="delete-button"
							className="del-button"
						>
							<div className="sc-dAlyuH select-svg">
								<svg
									stroke="currentColor"
									fill="currentColor"
									stroke-width="0"
									viewBox="0 0 24 24"
									height="20"
									width="20"
									xmlns="http://www.w3.org/2000/svg"
								>
									<path d="M7 4V2H17V4H22V6H20V21C20 21.5523 19.5523 22 19 22H5C4.44772 22 4 21.5523 4 21V6H2V4H7ZM6 6V20H18V6H6ZM9 9H11V17H9V9ZM13 9H15V17H13V9Z"></path>
								</svg>
							</div>
						</button>
						<button
							id="status-button"
							className="select-button"
						>
							<div className="sc-dAlyuH select-svg">
								<svg
									stroke="currentColor"
									fill="currentColor"
									stroke-width="0"
									viewBox="0 0 24 24"
									height="20"
									width="20"
									xmlns="http://www.w3.org/2000/svg"
								>
									<path d="M13 10H20L11 23V14H4L13 1V10Z"></path>
								</svg>
							</div>
						</button>
						<button
							id="theme-button"
							className="select-button"
						>
							<div className="sc-dAlyuH select-svg">
								<svg
									stroke="currentColor"
									fill="currentColor"
									stroke-width="0"
									viewBox="0 0 24 24"
									height="20"
									width="20"
									xmlns="http://www.w3.org/2000/svg"
								>
									<path d="M15.4565 9.67491L15.3144 9.53285C14.6661 8.90783 13.8549 8.43357 12.9235 8.18399C10.0168 7.40515 7.22541 9.05261 6.43185 12.0142C6.38901 12.1741 6.36574 12.3536 6.3285 12.805C6.17423 14.675 5.73449 16.0696 4.5286 17.4841C6.78847 18.3726 9.46572 18.9984 11.5016 18.9984C13.9702 18.9984 16.1644 17.3393 16.8126 14.9201C17.3306 12.9868 16.7513 11.018 15.4565 9.67491ZM13.2886 6.21289L18.2278 2.3713C18.6259 2.06168 19.1922 2.09694 19.5488 2.45355L22.543 5.44774C22.8997 5.80435 22.9349 6.3707 22.6253 6.76879L18.7847 11.7067C19.0778 12.895 19.0836 14.1719 18.7444 15.4377C17.8463 18.7896 14.8142 20.9984 11.5016 20.9984C8 20.9984 3.5 19.4966 1 17.9966C4.97978 14.9966 4.04722 13.1864 4.5 11.4966C5.55843 7.54646 9.34224 5.23923 13.2886 6.21289ZM16.7015 8.09149C16.7673 8.15494 16.8319 8.21952 16.8952 8.2852L18.0297 9.41972L20.5046 6.23774L18.7589 4.49198L15.5769 6.96685L16.7015 8.09149Z"></path>
								</svg>
							</div>
						</button>
						<button
							id="container-button"
							className="select-button"
						>
							<div className="sc-dAlyuH select-svg">
								<svg
									stroke="currentColor"
									fill="currentColor"
									stroke-width="0"
									viewBox="0 0 24 24"
									height="20"
									width="20"
									xmlns="http://www.w3.org/2000/svg"
								>
									<path d="M3 10H2V4.00293C2 3.44903 2.45531 3 2.9918 3H21.0082C21.556 3 22 3.43788 22 4.00293V10H21V20.0015C21 20.553 20.5551 21 20.0066 21H3.9934C3.44476 21 3 20.5525 3 20.0015V10ZM19 10H5V19H19V10ZM4 5V8H20V5H4ZM9 12H15V14H9V12Z"></path>
								</svg>
							</div>
						</button>
					</div>
				</div>
				<GuideTable />
			</div>
		</div>
	)
  };

export default guidesList
 