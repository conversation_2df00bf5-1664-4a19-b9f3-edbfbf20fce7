import { adminApiService, userApiService } from "./APIService";
import { TrainingDocument, TrainingDocumentUpload } from "../models/Training";
import axios from "axios";

export const getAllTrainingDocuments = async (
    setModels: any,
    setLoading: any,
    organizationId: string,
    skip: number,
    top: number,
    setTotalCount: any,
    orderByFields: string,
    filters: any[],
    accountId?: string
): Promise<void> => {
    try {
        setLoading(true);
        // If accountId is provided, use GetKnowledgeBaseList endpoint
            const response = await userApiService.get(`/KnowledgeBase/GetKnowledgeBaseList?accountId=${accountId}`);
            const apiData = response.data;

            if (Array.isArray(apiData)) {
                setModels(apiData);
                setTotalCount(apiData.length);
            }

    } catch (error) {
        console.error("Error fetching training documents:", error);
        setModels([]);
    } finally {
        setLoading(false);
    }
};

export const uploadSRSFile = async (accountId: string, formData: FormData) => {
	try {
		const response = await userApiService.post(
			`/Assistant/UploadSRSs?accountId=${encodeURIComponent(accountId)}`,
			formData,
			{
				headers: {
					'Content-Type': 'multipart/form-data',
				},
			}
		);
		return response;
	} catch (error) {
		// console.error('Error uploading SRS file:', error);
		throw error;
	}
};


export const uploadTrainingDocument = async (
  file:File,
  title:any,
  documentType:any,
  priority:any,
  organizationId:any,
  userId:any,
  accountId:any
) => {
  const formData = new FormData();
  formData.append('aifiles', file); // Must match the parameter name from [FromForm(Name = "aifiles")]

  // If needed, you can append other fields here
    formData.append('title', title);
    formData.append('documentType', documentType);
    formData.append('priority', priority);
    formData.append('accountId', accountId);

  const response = await userApiService.post(
    `/Assistant/UploadSRSs?accountId=${accountId}`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );

  return response.data;
};


export const updateTrainingDocument = async (
    documentId: string,
    title: string,
    documentType: string,
    priority: number,
    organizationId: string,
    updatedBy: string
): Promise<any> => {
    try {
        const requestData = {
            id: documentId,
            title,
            documentType,
            priority,
            organizationId,
            updatedBy
        };

        const response = await userApiService.put(`/Training/UpdateDocument`, requestData);
        return response.data;
    } catch (error) {
        console.error('Error updating training document:', error);
        throw error;
    }
};

export const replaceTrainingDocumentFile = async (
    documentId: string,
    file: File,
    title: string,
    documentType: string,
    priority: number,
    organizationId: string,
    updatedBy: string,
    accountId: string
): Promise<any> => {
    try {
        const formData = new FormData();
        formData.append('aifiles', file); // Must match the parameter name from [FromForm(Name = "aifiles")]
        formData.append('documentId', documentId); // Include document ID for replacement
        formData.append('title', title);
        formData.append('documentType', documentType);
        formData.append('priority', priority.toString());
        formData.append('organizationId', organizationId);
        formData.append('updatedBy', updatedBy);
        formData.append('accountId', accountId);
        formData.append('isReplacement', 'true'); // Flag to indicate this is a replacement

        // Use the same upload endpoint but with replacement parameters
        const response = await userApiService.post(
            `/Assistant/UploadSRSs?accountId=${accountId}&documentId=${documentId}&isReplacement=true`,
            formData,
            {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            }
        );

        return response.data;
    } catch (error) {
        console.error('Error replacing training document file:', error);
        throw error;
    }
};

export const downloadTrainingDocument = async (
    documentId: string,
    organizationId: string
): Promise<void> => {
    try {
        const response = await userApiService.get(`/Training/DownloadDocument/${documentId}?organizationId=${organizationId}`, {
            responseType: 'blob'
        });

        // Create blob link to download
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;

        // Get filename from response headers or use default
        const contentDisposition = response.headers['content-disposition'];
        let filename = 'document';
        if (contentDisposition) {
            const filenameMatch = contentDisposition.match(/filename="(.+)"/);
            if (filenameMatch) {
                filename = filenameMatch[1];
            }
        }

        link.setAttribute('download', filename);
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);
    } catch (error) {
        console.error('Error downloading training document:', error);
        throw error;
    }
};

export const DeleteKnowledgeBase = async (Id: any) => {

    try {
        const response = await userApiService.post(`/KnowledgeBase/Deleteknowledgebase?Id=${Id}`);
        if (response.status === 401)
        {
            localStorage.clear()
        }
        return response.data;
    } catch (error) {
        console.error("Error fetching guides:", error);
        return [];
    }
}
