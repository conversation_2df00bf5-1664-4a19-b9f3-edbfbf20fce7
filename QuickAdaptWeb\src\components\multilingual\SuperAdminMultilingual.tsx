import React, { useState, useEffect, startTransition } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Popover, Typography, Button, Avatar, Box, FormControl, InputLabel, Select, InputAdornment, IconButton, MenuItem, TextField } from '@mui/material';
import { getLanguages } from "../../services/MultilingualService";
import { quickadopt } from "../../assets/icons/icons";
import { Search } from "@mui/icons-material";

export interface Language {
  LanguageId: string;
  Language: string;
  LanguageCode: string;
  FlagIcon: string;
}


const SuperAdminMultiLingual= () => {
  const [openMenu, setOpenMenu] = useState(false);
  const [userType, setUserType] = useState("");
  const [anchorEl, setAnchorEl] = useState<HTMLDivElement | null>(null);
  const [openPopup, setOpenPopup] = useState(false);
  const navigate = useNavigate();
  const [toLanguage, setToLanguage] = useState<string>('en'); // Default language selection
  const [translatedLabels, setTranslatedLabels] = useState<string[]>([]);
  const location = useLocation();
  const [languages, setLanguages] = useState<Language[]>([]); // State to hold languages array
  const [loading, setLoading] = useState(true); // Loading state

  useEffect(() => {
    const userType = localStorage.getItem("userType") || "admin";
      setUserType(userType);
    //   localStorage.removeItem("languages");
    fetchLanguages();
 
  }, []);

  const fetchLanguages = async () => {
    try {
      const data = await getLanguages(); // Call the getLanguages function from API.tsx
        setLanguages(data);
      
            localStorage.setItem("languages", JSON.stringify(data));
    
    } catch (error) {
      console.error("Error fetching languages:", error);
    } finally {
      setLoading(false);
    }
  };

  const getLanguageName = (languageCode: string): string => {
    const language = languages.find(lang => lang.LanguageCode === languageCode);
    return language ? language.Language : "Unknown";
  };

  const handleToLanguageChange = async (e: any) => {
    const newToLanguage = e.target.value as string;
      setToLanguage(newToLanguage);
     navigate('/SuperAdminLanguageTranslater', { state: { toLanguage: newToLanguage } });
    };
    const [ageTwo, setAgeTwo] = useState(30);

 
  return (
    <div style={{marginTop:30}}>
      
         
            <div style={{ width: 123, height: 27, marginTop: 10 }}>
              <Box>
              <FormControl fullWidth>
              <InputLabel id="demo-simple-select-labels">Select Language to modify</InputLabel>
              <Select style={{ width: 656 }}
              
                labelId="demo-simple-select-labels"
                id="demo-simple-selects"
                defaultValue="English"
                label="Select Language to modify"
                value={toLanguage}
                onChange={handleToLanguageChange}
               
              >
                {languages.map(language => (
                      <MenuItem key={language.LanguageId} value={language.LanguageCode}>
                        {language.Language}
                      </MenuItem>
                    ))}
 
              </Select>
            
            </FormControl>
              </Box>
            </div>
           
       
    </div>
  );
};

export default SuperAdminMultiLingual;
