export interface TrainingDocument {
    Id: string;
    FileName: string;
    Title: string;
    DocumentType: string;
    Priority: number;
    UploadDate: string;
    FileSize: number;
    FileUrl: string;
    OrganizationId: string;
    CreatedBy: string;
    UpdatedBy: string;
    CreatedDate: string;
    UpdatedDate: string;
    Status: string;
}

export interface TrainingDocumentUpload {
    file: File;
    title: string;
    documentType: string;
    priority: number;
}

export interface DocumentType {
    value: string;
    label: string;
}

export const DocumentTypes: DocumentType[] = [
    { value: 'user_manual', label: 'User Manual' },
    { value: 'srs', label: 'SRS (Software Requirements Specification)' },
    { value: 'technical_doc', label: 'Technical Documentation' },
    { value: 'api_doc', label: 'API Documentation' },
    { value: 'training_material', label: 'Training Material' },
    { value: 'faq', label: 'FAQ' },
    { value: 'troubleshooting', label: 'Troubleshooting Guide' },
    { value: 'other', label: 'Other' }
];

export const PriorityLevels = [
    { value: 1, label: 'Low' },
    { value: 2, label: 'Medium' },
    { value: 3, label: 'High' },
    { value: 4, label: 'Critical' }
];
