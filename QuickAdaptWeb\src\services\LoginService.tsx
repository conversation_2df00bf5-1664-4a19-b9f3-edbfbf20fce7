import JSEncrypt from 'jsencrypt';
import { adminApiService, idsApiService } from "./APIService";

export const LoginService = async (
  emailId: string,
  password: string,
  organizationId: string,
  rememberLogin: boolean,
  returnUrl: string = "",
  authType: string,
  tenantid:string
): Promise<any> => {
  try {
    const requestUrl = process.env.REACT_APP_IDS_API + `/connect/token`;
    const data = new URLSearchParams({
      grant_type: 'password',
      client_id: 'dap_extension',
      client_secret: 'user_interaction',
      scope: 'openid profile api1',
      username: emailId,
      password: password,
      authType: authType,
      tenantid:tenantid
    });

    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
    };

    const response = await fetch(requestUrl, {
      method: 'POST',
      body: data,
      headers: headers,
    });

    if (response.status === 200) {
      const jsonResponse = await response.json(); 
      return jsonResponse; 
    } else {
      const errorResponse = await response.json(); 
      return errorResponse; 
    }
  }
  catch (error) {
    console.error("An error occurred:", error);
    throw error; 
  }
};
