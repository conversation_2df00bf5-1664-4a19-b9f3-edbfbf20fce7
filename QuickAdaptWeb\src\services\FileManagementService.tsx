import { adminApiService, userApiService, adminUrl } from "./APIService";
import { FileUpload } from "../models/FileUpload"; 

export const getAllFiles = async (): Promise<FileUpload[] | null> =>
	{
		try {
		  const response = await userApiService.get<FileUpload[]>('/FileUpload/GetAllFiles');
		  return response.data;
		} catch (error) {
		  console.error('Error fetching all users:', error);
		  throw error;
		}
	};
    
    export const UploadImage = async (
        files: File[],
        setLoading: any,
        setShowPopup: any,
        setModels: any,
        setselectedFile:any
    ) => {
        const formData = new FormData();

        for (let file of files) {
            formData.append("file", file);  // "files" matches backend parameter
        }
        //formData.append('file', files);
    
        try {
            setLoading(true);
    
            const response = await userApiService.post(`/FileUpload/UploadFiles`, formData, {
                headers: { "Content-Type": "multipart/form-data" }
            });
            const responseData = response.data;
            if (!responseData) {
                throw new Error('Network response was not ok');
            }
    
            setShowPopup(false);
           
        } catch (error) {

            throw error;
        } finally {
            setLoading(false);
        }
};
    

export const DeleteFile = async (fileId: any) => {
    try {    
        const response = await userApiService.delete('/FileUpload/DeleteFile', {
            params: {
                fileId
            }
        });
        // if ( response.status == 200) {
        //     return true;  
        // } else {
        //     console.log(response);
        // }
        return response.data;
    } catch (error) {
        console.error("Error deleting file", error);
        throw error;
    }
}

export const ReplaceFile = async (
    fileId: any,
    file: File,
    setLoading: any ) => {
        const formData = new FormData();

        
    formData.append("file", file); 
    
    try {
        setLoading(true);

        const response = await userApiService.put(
            "/FileUpload/ReplaceFile",
            formData,  
            {
              params: { fileId },  
              headers: { "Content-Type": "multipart/form-data" }
            }
          );

        const responseData = response.data;
            if (!responseData) {
                throw new Error('Network response was not ok');
            }
    
           
        } catch (error) {
            console.error('Error uploading file:', error);
             throw error;
        } finally {
            setLoading(false);
        }
    

}
    
export const GetGuidesUsedByFile = async(
    fileId:any,
    setLoading: any
)=>{
    try {
        const response = await userApiService.get('/FileUpload/GetGuidesUsedByFile', {
            params: {
                fileId
            }
        });
        return response.data;
    } catch (error) {
        console.error("Error Getting Guides ", error);
        throw error;
    }
}
    