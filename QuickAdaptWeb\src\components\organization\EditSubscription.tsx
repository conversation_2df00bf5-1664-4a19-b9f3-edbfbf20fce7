import React, { useEffect, useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Stack,
  MenuItem
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { adminUrl } from '../../services/APIService';
import dayjs from 'dayjs';


const EditSubscription = ({ showSubscribe, closeSubscribe,organizationId,setSnackbarOpen,setSnackbarSeverity,setSnackbarMessage,updateOrganizationDetails }: any) => {
  const [startDate, setStartDate] = useState();
  const [endDate, setEndDate] = useState(Date.now());
  const plans = ['Free Trail', 'Basic', 'Advanced'];

  const [organizationDetails, setOrganizationDetails] = useState({
    OrganizationId: "",
    Name: "",
    TimeZone: "Asia/Kolkata", // default value
    DateFormat: "dd-MM-yyyy",
    Logo: "",
    Status: "",
    CreatedDate: "",
    UpdatedDate: "",
    AuthorizationType: "",
    IsActive: "",
    Rtl: false,
    TimeFormat: "",
    ThemeId: "",
    Type: "",
    OrganizationPlanId: "",
    OrganizationPlan: {
      EndDate: "",
      StartDate: "",
    },
    Plan: "",
  });

  const [initialValues, setInitialValues] = useState({ ...organizationDetails });


  useEffect(() => {
    if (showSubscribe) {
      fetchOrganizationDetails(organizationId);
    }
  }, [showSubscribe, organizationId]);

  const fetchOrganizationDetails = async (id: any) => {
    try {
      const token = localStorage.getItem('access_token'); 
      const response = await fetch(`${adminUrl}/Organization/GetOrganizationById?organizationId=${id}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`, 
          'Content-Type': 'application/json' 
        }
      });
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      const data = await response.json();
      setOrganizationDetails({
        OrganizationId: data.OrganizationId,
        Name: data.Name,
        TimeZone: data.TimeZone,
        DateFormat: data.DateFormat,
        Logo: data.Logo,
        Status: data.Status,
        CreatedDate: data.CreatedDate,
        UpdatedDate: data.UpdatedDate,
        AuthorizationType: data.AuthorizationType,
        IsActive: data.IsActive,
        Rtl: data.RTL,
        TimeFormat: data.TimeFormat,
        ThemeId: data.ThemeId,
        Type: data.Type,
        OrganizationPlanId: data.OrganizationPlanId,
        OrganizationPlan: data.OrganizationPlan,
        Plan: data.Plan
      });
      setInitialValues({
        OrganizationId: data.OrganizationId,
        Name: data.Name,
        TimeZone: data.TimeZone,
        DateFormat: data.DateFormat,
        Logo: data.Logo,
        Status: data.Status,
        CreatedDate: data.CreatedDate,
        UpdatedDate: data.UpdatedDate,
        AuthorizationType: data.AuthorizationType,
        IsActive: data.IsActive,
        Rtl: data.RTL,
        TimeFormat: data.TimeFormat,
        ThemeId: data.ThemeId,
        Type: data.Type,
        OrganizationPlanId: data.OrganizationPlanId,
        OrganizationPlan: data.OrganizationPlan,
        Plan: data.Plan
      });
      setStartDate(data.OrganizationPlan?.StartDate ?? Date.now());
      setEndDate(data.OrganizationPlan?.EndDate ?? Date.now());
    } catch (error) {
      console.error("Failed to fetch organization details:", error);
    }
  };

  const setPlanType = (plan: any) => {
    setInitialValues((initialValue) => ({
      ...initialValue,
      Plan: plan,
    }));
  }

  const updateSubscription = () => {
    if (initialValues.Plan == "Free Trail") {
      setSnackbarMessage("You can't Select/Upgrade Free Trail Again");
			setSnackbarSeverity("error");
			setSnackbarOpen(true);
    } 
    else if(dayjs(endDate).diff(dayjs(startDate),'day')<15){
      setSnackbarMessage("The Subscription Update Should be above 15 Days");
			setSnackbarSeverity("error");
			setSnackbarOpen(true);
    } else {
      setOrganizationDetails((orgDetails) => ({
        ...orgDetails,
        Plan: initialValues.Plan,
        OrganizationPlan: {
          StartDate: dayjs(startDate).format("YYYY-MM-DDTHH:mm:ss.SSSZ"),
          EndDate: dayjs(endDate).format("YYYY-MM-DDTHH:mm:ss.SSSZ")
        }
      }));

      try {
        updateOrganizationDetails(
          {
            ...organizationDetails,
            Plan: initialValues.Plan,
            OrganizationPlan: {
              StartDate: dayjs(startDate).format("YYYY-MM-DDTHH:mm:ss.SSSZ"),
              EndDate: dayjs(endDate).format("YYYY-MM-DDTHH:mm:ss.SSSZ")
            }
          }
        );
      } catch (error) {
        console.error("Failed to update organization:", error);
        setSnackbarMessage("Failed to update organization");
        setSnackbarSeverity("error");
        setSnackbarOpen(true);
      }


    }
  }

  
  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
    <Box display="flex" justifyContent="center" alignItems="center" height="100vh">
       <Dialog
        open={showSubscribe}
        onClose={closeSubscribe}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            p: 2,
            borderRadius: 2,
          },
        }}
      >
        <DialogTitle>Update Subscription</DialogTitle>
        <DialogContent dividers>
            <Stack spacing={2}>
            <TextField
                select
                label="Select Plan"
                fullWidth
                value={initialValues.Plan}
                onChange={(e) => setPlanType(e.target.value)}
              >
                {plans.map((p) => (
                  <MenuItem key={p} value={p}>
                    {p}
                  </MenuItem>
                ))}
              </TextField>
              <DatePicker
                label="Start Date"
                value={dayjs(startDate) }
                onChange={(date: any) => setStartDate(date)}
                readOnly
                slotProps={{ textField: { fullWidth: true,disabled: true } }}
              />
              <DatePicker
                label="End Date"
                value={dayjs(endDate)}
                onChange={(date:any) => setEndDate(date)}
                slotProps={{ textField: { fullWidth: true } }}
              />
            </Stack>
          </DialogContent>
              
        <DialogActions>
          <Button onClick={closeSubscribe}>Cancel</Button>
          <Button onClick={updateSubscription} variant="contained">
            Save
          </Button>
        </DialogActions>
      </Dialog>
      </Box>
      </LocalizationProvider>
  );
};

export default EditSubscription;
