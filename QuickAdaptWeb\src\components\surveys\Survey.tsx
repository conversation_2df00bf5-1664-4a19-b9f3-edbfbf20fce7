  
  
import React, { useState } from 'react';

import { isSidebarOpen } from "../adminMenu/sidemenustate";
import { Container } from '@mui/material';
import { useTranslation } from 'react-i18next';
const Surveys = () => {
  const { t: translate } = useTranslation();
  const [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());
  return (
    <Container maxWidth="xl">
   
       <div className={`smooth-transition`} style={{marginLeft:sidebarOpen?"170px":""}}>
        <center><h1>{translate('Surveys')} </h1></center>
      </div>
   
      </Container>
    );
  };
  

export default Surveys