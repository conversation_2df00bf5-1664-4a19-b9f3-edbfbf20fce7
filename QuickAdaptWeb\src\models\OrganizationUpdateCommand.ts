export interface OrganizationUpdateCommand {
	OrganizationId: string;
	Name: string;
	DefaultLocation: string;
	TimeZone: string;
	DateFormat: string;
	Domain: string;
	EnableRegisterUsers: boolean;
	EnableAppData: boolean;
	Logo: string;
	EnableCustomMenu: boolean;
	TaskCompletionRedirection: string;
	ElementControlStyle: string;
	DropDownBehaviour: string;
	Rtl: boolean;
	IsCaptchaEnabled: boolean;
	TimeFormat: string;
	ShowTerms: boolean;
	EnableNumberFormat: boolean;
	ThemeId: string;
	CanUserChangeTheme: boolean;
	ThemeType: string;
	BillingId: string;
	Poc: boolean;
	Type: string;
	MenuType: string;
	EnableMobile: boolean;
	IsChatbotEnabled: boolean;
	GridViewPageSize?: number;
	ReportsPageSize?: number;
	AppGridControlPageSize?: number;
	WhiteLabel: boolean;
	DurationFormat: string;
	ReadOnlyFormat: string;
	IsCommentsEnabled: boolean;
	EnableCommentLevel: string;
	IsShowHideOtherEnabled: boolean;
	IsCustomUserListEnabled: boolean;
	SandboxType: string;
	IsBiEnabled: boolean;
	DashboardViewType: string;
	CanUserChangeDashboadView: boolean;
	IsDeleted: boolean;
	CreatedDate: Date;
	UpdatedDate: Date;
	OrganizationPlanId: string;
	IsActive: boolean;
	OtpTime: string;
	EnableBatteryHealthCheck: boolean;
	EnablePagewiseData: boolean;
	MfaLevelSelected: string;
	IsMFAEnabled: boolean;
	ToastNotificationEnabled: boolean;
}