import React from 'react';
import { Box, Paper, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

interface CardProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  shadow?: 'none' | 'sm' | 'md' | 'lg';
  className?: string;
  onClick?: () => void;
  hover?: boolean;
}

const StyledCard = styled(Paper)<{
  cardPadding: string;
  cardShadow: string;
  hover: boolean;
}>(({ theme, cardPadding, cardShadow, hover }) => ({
  borderRadius: 'var(--radius-lg)',
  border: '1px solid var(--color-gray-200)',
  backgroundColor: 'var(--color-white)',
  transition: 'var(--transition-normal)',
  cursor: hover ? 'pointer' : 'default',
  
  ...(cardShadow === 'none' && { boxShadow: 'none' }),
  ...(cardShadow === 'sm' && { boxShadow: 'var(--shadow-sm)' }),
  ...(cardShadow === 'md' && { boxShadow: 'var(--shadow-md)' }),
  ...(cardShadow === 'lg' && { boxShadow: 'var(--shadow-lg)' }),
  
  ...(cardPadding === 'none' && { padding: 0 }),
  ...(cardPadding === 'sm' && { padding: 'var(--spacing-4)' }),
  ...(cardPadding === 'md' && { padding: 'var(--spacing-6)' }),
  ...(cardPadding === 'lg' && { padding: 'var(--spacing-8)' }),
  
  ...(hover && {
    '&:hover': {
      boxShadow: 'var(--shadow-lg)',
      transform: 'translateY(-1px)',
    },
  }),
}));

const CardHeader = styled(Box)({
  marginBottom: 'var(--spacing-4)',
});

const CardTitle = styled(Typography)({
  fontSize: 'var(--font-size-lg)',
  fontWeight: 'var(--font-weight-semibold)',
  color: 'var(--color-gray-900)',
  lineHeight: 'var(--line-height-tight)',
  margin: 0,
});

const CardSubtitle = styled(Typography)({
  fontSize: 'var(--font-size-sm)',
  fontWeight: 'var(--font-weight-normal)',
  color: 'var(--color-gray-600)',
  lineHeight: 'var(--line-height-normal)',
  marginTop: 'var(--spacing-1)',
});

const Card: React.FC<CardProps> = ({
  children,
  title,
  subtitle,
  padding = 'md',
  shadow = 'md',
  className,
  onClick,
  hover = false,
}) => {
  return (
    <StyledCard
      cardPadding={padding}
      cardShadow={shadow}
      hover={hover}
      className={className}
      onClick={onClick}
      elevation={0}
    >
      {(title || subtitle) && (
        <CardHeader>
          {title && <CardTitle variant="h6">{title}</CardTitle>}
          {subtitle && <CardSubtitle variant="body2">{subtitle}</CardSubtitle>}
        </CardHeader>
      )}
      {children}
    </StyledCard>
  );
};

export default Card;
