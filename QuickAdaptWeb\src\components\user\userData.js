export const userData = {
	Msg_Code: "802.171.248.01",
	data: [
		{
			UserId: "19042024-*********-bc71e888-276a-4743-9592-5ead3281579a",
			FirstName: "Aparna",
			LastName: "Aparna ST",
			UserName: "aparnaaparna st6212",
			EmailId: "<EMAIL>",
			ContactNumber: "9999999999",
			EmergencyContactNumber: null,
			CompanyName: "Aparna ST",
			EmailConfirmed: true,
			AdminDeactivated: false,
			CreatedOn: "2024-04-19T03:38:04",
			ProfilePhoto1:
				"FileUploads/19042024-*********-de73d16a-e897-4caf-86f6-361a3e7a9bdf/User/*********-AdminProfile.jpg",
			ProfilePhoto2:
				"FileUploads/19042024-*********-de73d16a-e897-4caf-86f6-361a3e7a9bdf/User/*********-AdminProfile.jpg",
			UserType: "Admin",
			OrganizationId: "19042024-*********-de73d16a-e897-4caf-86f6-361a3e7a9bdf",
			Manager: "<EMAIL>",
			EmployeeId: null,
			EmployeeCode: null,
			Gender: null,
			TimeZone: "India Standard Time",
			DateofBirth: null,
			EnableAppData: true,
			IsBlocked: false,
			BlockedDateTime: null,
			HasUserRole: true,
			AcceptedTerms: false,
			IsMultiFactorEnabled: false,
			CalendarId: null,
			HasIntegrationAccess: true,
			RTL: false,
			ThemeId: null,
			MobileConfirmed: false,
			FullName: "Aparna Aparna ST",
			CustomUserFieldsData: {},
			LoginType: "Regular",
		},
		{
			UserId: "19042024-*********-716d6610-27ae-4b39-85ff-b485f4ac32ff",
			FirstName: "Aparna",
			LastName: "User",
			UserName: "aparnauser6894",
			EmailId: "<EMAIL>",
			ContactNumber: "9797979797",
			EmergencyContactNumber: "",
			CompanyName: "",
			EmailConfirmed: true,
			AdminDeactivated: false,
			CreatedOn: "2024-04-19T04:08:22",
			ProfilePhoto1: null,
			ProfilePhoto2: null,
			UserType: "User",
			OrganizationId: "19042024-*********-de73d16a-e897-4caf-86f6-361a3e7a9bdf",
			Manager: "<EMAIL>",
			EmployeeId: "",
			EmployeeCode: "",
			Gender: "",
			TimeZone: "India Standard Time",
			DateofBirth: null,
			EnableAppData: true,
			IsBlocked: false,
			BlockedDateTime: null,
			HasUserRole: true,
			AcceptedTerms: false,
			IsMultiFactorEnabled: false,
			CalendarId: "",
			HasIntegrationAccess: false,
			RTL: false,
			ThemeId: null,
			MobileConfirmed: false,
			FullName: "Aparna User",
			CustomUserFieldsData: {},
			LoginType: "",
		},
		{
			UserId: "19042024-*********-b96a5ffb-e22d-4624-92f1-c382cdcfbfe2",
			FirstName: "Aparna",
			LastName: "Workspace",
			UserName: "aparnaworkspace7442",
			EmailId: "<EMAIL>",
			ContactNumber: "9700888868",
			EmergencyContactNumber: "",
			CompanyName: "",
			EmailConfirmed: true,
			AdminDeactivated: false,
			CreatedOn: "2024-04-19T04:07:30",
			ProfilePhoto1: null,
			ProfilePhoto2: null,
			UserType: "User",
			OrganizationId: "19042024-*********-de73d16a-e897-4caf-86f6-361a3e7a9bdf",
			Manager: "<EMAIL>",
			EmployeeId: "",
			EmployeeCode: "",
			Gender: "",
			TimeZone: "India Standard Time",
			DateofBirth: null,
			EnableAppData: true,
			IsBlocked: false,
			BlockedDateTime: null,
			HasUserRole: true,
			AcceptedTerms: false,
			IsMultiFactorEnabled: false,
			CalendarId: "",
			HasIntegrationAccess: false,
			RTL: false,
			ThemeId: null,
			MobileConfirmed: false,
			FullName: "Aparna Workspace",
			CustomUserFieldsData: {},
			LoginType: "",
		},
		{
			UserId: "30042024-*********-55f80efa-85bb-42e4-8751-b29173357a13",
			FirstName: "test",
			LastName: "user",
			UserName: "testuser6477",
			EmailId: "<EMAIL>",
			ContactNumber: "9874561230",
			EmergencyContactNumber: "",
			CompanyName: "",
			EmailConfirmed: true,
			AdminDeactivated: false,
			CreatedOn: "2024-04-30T08:54:19",
			ProfilePhoto1: null,
			ProfilePhoto2: null,
			UserType: "User",
			OrganizationId: "19042024-*********-de73d16a-e897-4caf-86f6-361a3e7a9bdf",
			Manager: "<EMAIL>",
			EmployeeId: "",
			EmployeeCode: "",
			Gender: null,
			TimeZone: "India Standard Time",
			DateofBirth: null,
			EnableAppData: true,
			IsBlocked: false,
			BlockedDateTime: null,
			HasUserRole: false,
			AcceptedTerms: false,
			IsMultiFactorEnabled: false,
			CalendarId: null,
			HasIntegrationAccess: false,
			RTL: false,
			ThemeId: null,
			MobileConfirmed: false,
			FullName: "test user",
			CustomUserFieldsData: {
				Two: "1",
				One: "new",
			},
			LoginType: "Regular",
		},
	],
	count: 4,
}
