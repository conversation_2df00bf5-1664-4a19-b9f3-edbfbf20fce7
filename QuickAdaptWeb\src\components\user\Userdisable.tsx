import React, { useState, useEffect, ChangeEvent, FocusEvent, FormEvent } from "react";
import {
	TextField,
	Button,
	Select,
	MenuItem,
	FormControl,
	InputLabel,
	FormHelperText,
	SelectChangeEvent,
	Snackbar,
	Alert,
	Box,
} from "@mui/material";
import { deactivateUser } from "../../services/UserService";
import { adminUrl } from "../../services/APIService";
import { useSnackbar } from "../../SnackbarContext";
import IconButton from "@mui/material/IconButton";
import InputAdornment from "@mui/material/InputAdornment";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import Visibility from "@mui/icons-material/Visibility";
import { JSEncrypt } from "jsencrypt";
import NoAccountsIcon from "@mui/icons-material/NoAccounts";
import styles from "./UserStyles.module.scss";
import { useAuth } from "../auth/AuthProvider";

type InputFields = {
	Password: string;
};

type ErrorFields = Partial<InputFields>;


const UserDisable = (props: any) => {
	const {
		disableUserPopup,
		setDisableUserPopup,
		userId,
		fetchUsersList,
		setModels,
		setLoading,
		skip,
		top,
		//Organizationid,
		setTotalcount,
		orderByFields,
		filters,
	} = props;
	const { openSnackbar } = useSnackbar();
	const { signOut, userDetails } = useAuth();
	const [OrganizationId, setOrganizationId] = useState(userDetails?.OrganizationId??"");


	const handleSubmit = async (e: any) => {
		e.preventDefault();

		setLoading(true);
		try {
			await deactivateUser(
				setModels,
				setLoading,
				OrganizationId,
				skip,
				top,
				setTotalcount,
				userId,
				openSnackbar,
				orderByFields,
				filters
			);
			setLoading(false);
			setDisableUserPopup(false);
		} catch (error) {
			throw error;

		}
	};

	return (
		disableUserPopup && (
			<div className="qadpt-modal-overlay">
			<div className="qadpt-usrconfirm-popup qadpt-danger">
  <div>
    <div className="qadpt-icon">
      <NoAccountsIcon />
    </div>
  </div>
  <div>
    <div className="qadpt-popup-title">Disable User</div>
    {/* <div className="qadpt-warning-wrapper">
      <p className="qadpt-warning">
       
      </p>
      <svg
        onClick={() => setDisableUserPopup(false)}
        className="qadpt-close-icon"
        xmlns="http://www.w3.org/2000/svg"
        x="0px"
        y="0px"
        width="24"
        height="24"
        viewBox="0 0 50 50"
      >
        <path d="M 7.71875 6.28125 L 6.28125 7.71875 L 23.5625 25 L 6.28125 42.28125 L 7.71875 43.71875 L 25 26.4375 L 42.28125 43.71875 L 43.71875 42.28125 L 26.4375 25 L 43.71875 7.71875 L 42.28125 6.28125 L 25 23.5625 Z"></path>
      </svg>
					</div> */}
					<div className="qadpt-warning">
					Disabling this user will remove all his access from this platform.
								</div>
    <form onSubmit={handleSubmit}>
      <div className="qadpt-buttons">
        <button
          onClick={() => setDisableUserPopup(false)}
          className="qadpt-cancel-button"
        >
          Cancel
        </button>
        <button
          className="qadpt-conform-button"
          type="submit"
        >
          Disable
        </button>
      </div>
    </form>
  </div>
				</div>
				</div>

		)
	);
};

export default UserDisable;
