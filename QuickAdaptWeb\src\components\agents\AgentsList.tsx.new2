	return (
		<Container>
			<div className="qadpt-midpart setng-box">
				<div className="qadpt-content-block">
					<div>
						<div className="qadpt-head">
							<div className="qadpt-title-sec">
								<div className="qadpt-title">{translate('Agents')}</div>
								<div className="qadpt-description">{translate('View and manage your list of agents')}</div>
							</div>
							<div className="qadpt-right-part">
								<button
									className="qadpt-memberButton"
								>
									<i className="fal fa-add-plus"></i>
									<span>{translate('Create Agent')}</span>
								</button>
							</div>
							<div></div>
						</div>
						<div>
							{showPopup ? (
								<CreateAccount
									setModels={setModels}
									setLoading={setLoading}
									showPopup={showPopup}
									setShowPopup={setShowPopup}
									orderByField={orderByFields}
								/>
							) : (
								""
							)}
						</div>

						{showeditPopup ? (
							<EditAccount
								showEditPopup={showeditPopup}
								setShowEditPopup={setShowEditPopup}
								accountidedit={accountidedit}
								GetAllAccounts={GetAllAccounts}
								setModels={setModels}
								setLoading={setLoading}
								Organizationid={Organizationid}
								skip={skip}
								top={top}
								setTotalcount={setTotalcount}
								orderByField={orderByFields}
								filters={filters}
								setFilters={setFilters}
							/>
						) : (
							""
						)}

						<div>
							<Box className="qadpt-content-box">
								<div className="qadpt-src-flt">
									<div>
									<TextField
											className={`qadpt-teamsearch ${errors.SearchInput ? "qadpt-teamsearch-error" : ""}`}
											name="SearchInput"
											value={inputs.SearchInput}
											onChange={(e) => {
												const newValue = e.target.value;
												setInputs({ ...inputs, SearchInput: newValue });
												if (newValue === "") {
													clearSearch();
													GetSystemPromptsList(
														setModels,
														setLoading,
														OrganizationIds,
														AccountIds,
														skip,
														top,
														setTotalcount,
														orderByFields,
														filters
													);
												}
											}}
											onKeyDown={(e) => {
												if (e.key === 'Enter') {
													globalhandleSearch(inputs.SearchInput);
												}
												}}
											placeholder={translate('Search agents by name')}
											variant="outlined"
											error={!!errors.SearchInput}
											InputProps={{
												startAdornment: (
													<InputAdornment position="start">
														<IconButton
															aria-label="search"
															onClick={() => globalhandleSearch(inputs.SearchInput)}
															onMouseDown={(event) => event.preventDefault()}
														>
															<SearchIcon />
														</IconButton>
													</InputAdornment>
												),
												endAdornment: inputs.SearchInput && (
													<InputAdornment position="end">
														<IconButton
															aria-label="clear"
															onClick={() => {
																setInputs({ ...inputs, SearchInput: "" });
																clearSearch();
																GetSystemPromptsList(
																	setModels,
																	setLoading,
																	OrganizationIds,
																	AccountIds,
																	skip,
																	top,
																	setTotalcount,
																	orderByFields,
																	filters
																);
															}}
														>
															<ClearIcon />
														</IconButton>
													</InputAdornment>
												),
											}}
										/>
									</div>{" "}
								</div>
								<DataGrid
									className="qadpt-account-grd"
									rows={models} // Display models if available
									columns={columns}
									pagination
									paginationMode="server"
									slots={{
										columnMenu: (menuProps) =>
											menuProps.colDef.field === "AccountName" ? (
												<AccountCustomColumnMenu
													column={menuProps.colDef.field}
													setModels={setModels}
													setLoading={setLoading}
													skip={skip}
													top={top}
													OrganizationId={Organizationid}
													setTotalcount={setTotalcount}
													orderByFields={orderByFields}
													filters={filters}
													setFilters={setFilters}
													{...menuProps}
													options={models.map((model: any) => model.AccountName || "")}
													onSearch={handleSearch}
												/>
											) : null,
									}}
									getRowId={(row) => row.Id || row.AccountId}
									paginationModel={paginationModel}
									onPaginationModelChange={(model) => {
										setPaginationModel(model);
									}}
									rowCount={totalcount}
									pageSizeOptions={[15, 25, 50, 100]}
									localeText={{
										MuiTablePagination: {
											labelRowsPerPage: translate("Records Per Page"),
										},
									}}
									disableRowSelectionOnClick={true}
									loading={loading} // Show the DataGrid's built-in loading overlay
									sortModel={sortModel}
									onSortModelChange={handleSortModelChange}
									autoHeight
									density="standard"
								/>
								{models.length === 0 && !loading && (
									<div className="Loaderstyles">
										<img
											src={loader}
											alt="Spinner"
											className="LoaderSpinnerStyles"
										/>
									</div>
								)}
							</Box>
						</div>

						{showDeletePopup ? (
							<div className="qadpt-modal-overlay">
							<div className="qadpt-usrconfirm-popup qadpt-danger">
							<div>
								<div className="qadpt-icon">
									<i className='fal fa-trash-alt'></i>
								</div>
								</div>
								<div className="qadpt-popup-title">Delete Account</div>


								<div className="qadpt-warning">
									The Account cannot be restored once it is deleted.
								</div>
								<div className="qadpt-buttons">
									<button
										 onClick={() => setShowDeletePopup(false)}
										 className="qadpt-cancel-button"
										>
										Cancel
									</button>

									<button
										className="qadpt-conform-button"
										onClick={handleDeleteAccount}
									>
										Delete
									</button>
								</div>

						</div>
								</div>

						) : (
							""
						)}
					</div>
					<Snackbar
						open={snackbarOpen}
						autoHideDuration={4000}
						onClose={handleSnackbarClose}
						anchorOrigin={{ vertical: "top", horizontal: "right" }}
					>
						<Alert
							onClose={handleSnackbarClose}
							severity={snackbarSeverity}
						>
							{snackbarMessage}
						</Alert>
					</Snackbar>
				</div>
			</div>
		</Container>
	);
};

export default AgentsList;
