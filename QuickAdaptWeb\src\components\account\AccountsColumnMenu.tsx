import React from "react";
import { GridColumnMenu, GridColumnMenuHideItem, GridColumnMenuProps } from "@mui/x-data-grid";
//import CustomColumnMenuUserItem from "../CustomColumnmenuuserItem";
import AccountCustomColumnMenuUserItem from "./AccountCustomColumnMenuUserItem";

const AccountCustomColumnMenu: React.FC<
	GridColumnMenuProps & {
		column: any;
		skip: any;
		top: any;
		OrganizationId: any;
		setTotalcount: any;
		orderByFields: any;
		setModels: any;
		setLoading: any;
		filters: any;
		setFilters: any;
		options: string[];
		onSearch: (value: string[]) => void;
	}
> = (props) => {
	const {
		column,
		skip,
		top,
		OrganizationId,
		setTotalcount,
		orderByFields,
		setModels,
		setLoading,
		filters,
		setFilters,
		options,
		onSearch,
		...other
	} = props;

	return (
		<GridColumnMenu
			{...other}
			slots={{
				columnMenuUserItem: (menuProps) => (
					<AccountCustomColumnMenuUserItem
						column={column}
						setModels={setModels}
						setLoading={setLoading}
						skip={skip}
						top={top}
						OrganizationId={OrganizationId}
						setTotalcount={setTotalcount}
						orderByFields={orderByFields}
						filters={filters}
						setFilters={setFilters}
						options={options}
						onSearch={onSearch}
					/>
				),
				GridColumnMenuHideItem: null,
			}}
			slotProps={{
				columnMenuUserItem: {
					displayOrder: 15,
				},
			}}
		/>
	);
};

export default AccountCustomColumnMenu;
