import { Box, TextField, InputAdornment, FormControl, InputLabel, MenuItem, IconButton } from "@mui/material";
import Select, { SelectChangeEvent } from "@mui/material/Select";
import React, { useEffect, useState } from "react";
import languages from "../../languages";

import { Search } from "@mui/icons-material";
import { getLanguages, getPlatformLabels } from "../../services/MultilingualService"
import { useLocation } from "react-router-dom";
interface Language {
    LanguageId: string;
    Language: string;
    LanguageCode: string;
    FlagIcon: string;
  }
  
  
const SuperAdminLanguageTranslater = () =>
{
    const location = useLocation();
 
    const [ageTwo, setAgeTwo] = useState(30);
    const initialToLanguage = location.state?.toLanguage || 'en';
    const [toLanguage, setToLanguage] = useState<string>(initialToLanguage); // Initialize with value from location.state
    const [labels, setLabels] = useState<any>({});
    const userType = localStorage.getItem("userType");
    const [languages, setLanguages] = useState<Language[]>([]);

  useEffect(() => {
    const storedLanguages = localStorage.getItem("languages");
    if (storedLanguages) {
      setLanguages(JSON.parse(storedLanguages));
    }
  }, []);
     
    
      const getLanguageName = (languageCode: string): string => {
        const language = languages.find(lang => lang.LanguageCode === languageCode);
        return language ? language.Language : "Unknown";
      };
    
      const handleToLanguageChange = async (e: any) => {
        const newToLanguage = e.target.value as string;
          setToLanguage(newToLanguage);
        
    };
    const ToLanguageNew=getLanguageName(toLanguage)
useEffect(() => {
   
    const fetchLabels = async (ToLanguageNew: string) => {
      try {
        const data = await getPlatformLabels(ToLanguageNew);
        if (data) {
          let parsedLabels;
          if (ToLanguageNew === "English") {
            parsedLabels = JSON.parse(data.en);
          } else if (ToLanguageNew === "Hindi") {
            parsedLabels = JSON.parse(data.hi);
          } else if (ToLanguageNew === "Telugu") {
            parsedLabels = JSON.parse(data.te);
          } else if (ToLanguageNew === "Arabic") {
            parsedLabels = JSON.parse(data.ar);
          }

          if (parsedLabels) {
            setLabels(parsedLabels);
          }
        }
      } catch (error) {
      }
    };
    
    fetchLabels(ToLanguageNew);
}, [ToLanguageNew]);

const [searchTerm, setSearchTerm] = useState<string>(''); 
const handleSearchChange = (e: any) => {
    setSearchTerm(e.target.value);
    };
    const filterLabels = (labels: any, term: string) => {
        if (!term) return labels;
    
        const lowercasedTerm = term.toLowerCase();
        const filteredLabels: any = {};
    
        Object.keys(labels).forEach(key => {
          if (typeof labels[key] === 'object') {
            const nestedLabels = filterLabels(labels[key], term);
            if (Object.keys(nestedLabels).length > 0) {
              filteredLabels[key] = nestedLabels;
            }
          } else if (key.toLowerCase().includes(lowercasedTerm) || labels[key].toLowerCase().includes(lowercasedTerm)) {
            filteredLabels[key] = labels[key];
          }
        });
    
        return filteredLabels;
      };

      const renderLabels = (labels: any) => {
        return Object.keys(labels).map((key) => {
          const value = labels[key];
          if (typeof value === "object") {
            return (
              <ul key={key}>
                {Object.keys(value).map((nestedKey) => (
                  <MenuItem key={nestedKey}>{value[nestedKey]}</MenuItem>
                ))}
              </ul>
            );
          } else {
            return <MenuItem key={key}>{value}</MenuItem>;
          }
        });
      };
    
      const renderLabelsKeys = (labels: any) => {
        return Object.keys(labels).map((key) => {
          const value = labels[key];
          if (typeof value === "object") {
            return (
              <ul key={key}>
                {Object.keys(value).map((nestedKey) => (
                  <MenuItem key={nestedKey}>{nestedKey}</MenuItem>
                ))}
              </ul>
            );
          } else {
            return <MenuItem key={key}>{key}</MenuItem>;
          }
        });
      };
    
      const filteredLabels = filterLabels(labels, searchTerm);
  
      
  
   
    return (
        <div>
 {userType === "superadmin" ?
        (
            <div style={{ marginTop: 90, marginLeft: 27 }}>
            <h2>Language Translation</h2>
            <p style={{ marginTop: -15 }}>select the secondary language to modify</p>
            <p></p>
            
            <button className={`smooth-transition`} style={{
              position: "absolute",
              zIndex: "99999",
              cursor: "pointer",
              fontSize: "14px",
              background: "transparent",
              color: "black",
              fontWeight: "normal",
              border: "1px solid grey",
              marginLeft: "1162px",
              marginTop: "-62px",
              borderRadius: "5px",
              padding: "10px 31px"
            }}>Save</button>
      
            <div style={{ marginTop: 54 }}>
              <Box sx={{ padding: 1 }}>
                <TextField
                  variant="outlined"
                  placeholder="Search "
                  fullWidth
                style={{ marginTop: -70, marginLeft: 1027 }}
                value={searchTerm}
                onChange={handleSearchChange}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <Search sx={{ color: 'black' }} />
                      </InputAdornment>
                    ),
                    sx: {
                      borderRadius: '5px',
                      backgroundColor: 'rgba(255, 255, 255, 0.1)',
                      border: '1px solid black',
                      height: '45px',
                      marginTop: '10px',
                      width: '200px',
                      color: 'black'
                    },
                    inputProps: {
                      sx: {
                        fontSize: '0.875rem',
                      },
                    },
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '& fieldset': {
                        borderColor: 'transparent',
                      },
                      '&:hover fieldset': {
                        borderColor: 'transparent',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: 'transparent',
                      },
                      backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    },
                    '& .MuiInputBase-input': {
                      color: 'black',
                    },
                    '& .MuiInputBase-input::placeholder': {
                      color: 'black',
                      opacity: 0.7,
                      fontSize: '0.850rem',
                    },
                  }}
                />
              </Box>
            </div>
      
            <div style={{ marginTop: 14, display: 'flex', justifyContent: 'space-between' }}>
             
                <div style={{ width: 630, backgroundColor: "aliceblue" }}>
                  <Box>
                    <FormControl variant="standard" fullWidth>
                      <InputLabel id="demo-simple-select-readonly-label"></InputLabel>
                      <Select
                       
                        style={{ width: 600 }}
                        labelId="demo-simple-select-readonly-label"
                        id="demo-simple-select-readonly"
                        value={ageTwo}
                        inputProps={{ readOnly: true }}
                      >
                        <MenuItem value="">
                          <em>None</em>
                        </MenuItem>
                        <MenuItem value={10}>Telugu</MenuItem>
                        <MenuItem value={20}>Hindi</MenuItem>
                        <MenuItem value={30}>English</MenuItem>
                      </Select>
                   
                     
                      <div>{renderLabelsKeys(labels)}</div>
                    
                     
                   
                    </FormControl>
                  </Box>
                </div>
            
      
             
      
      
      
      
      
      
      
      
      
              <div style={{ width: 698, marginLeft: 39 }}>
                <Box>
                  <FormControl fullWidth>
                    <InputLabel id="demo-simple-select-labels">Select Language to modify</InputLabel>
                    <Select style={{ width: 656 }}
                      labelId="demo-simple-select-labels"
                      id="demo-simple-selects"
                      defaultValue="English"
                      label="Select Language to modify"
                      value={toLanguage}
                      onChange={handleToLanguageChange}
                      
                    >
                      {languages.map(language => (
                      <MenuItem key={language.LanguageId} value={language.LanguageCode}>
                        {language.Language}
                      </MenuItem>
                    ))}
                    </Select>
                    <div>{renderLabels(labels)}</div>
                  </FormControl>
                </Box>
              </div>
            </div>
      
           
            
          </div>
          
        ): (
            null
      )}
         
        </div>
    );
}
export default SuperAdminLanguageTranslater