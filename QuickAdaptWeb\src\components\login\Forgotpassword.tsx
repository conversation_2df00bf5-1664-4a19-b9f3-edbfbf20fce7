import React, { useState } from 'react';
import { Con<PERSON><PERSON>, <PERSON><PERSON>ield, Button, Typography, Link, Box } from '@mui/material';
import { sendForgotPasswordEmail } from '../../services/ForgotPasswordService';

function ForgotPassword() {
    const [email, setEmail] = useState('');
    const [submitted, setSubmitted] = useState(false);
    const [Errorfiled, setErrorField] = useState(" ");
    const handleSubmit = async (event: React.FormEvent) => {
        event.preventDefault();
        try {
            const response = await sendForgotPasswordEmail(email);  
            if (response.Success) { 
                setSubmitted(true);
            }else{
                setErrorField(response.ErrorMessage);
            }
        } catch (error) {
            console.error("Error sending email", error);
        }
    };
    const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setEmail(e.target.value);
        setErrorField(''); 
    };

    return (
        <Container maxWidth="xs" sx={{ marginTop: "60px"}}>
            {!submitted ? (
                <form onSubmit={handleSubmit}>
                    <Typography
                        variant="h5"
                        gutterBottom
                        sx={{
                            fontFamily: 'Syncopate',
                            fontSize: '19px',
                            fontWeight: 700,
                            lineHeight: '19.78px',
                            letterSpacing: '0.3px',
                            textAlign: 'center',
                            color: "#5F9EA0",
                        }}
                    >
                        QuickAdopt
                    </Typography>

                    <Box
                        sx={{
                            marginTop: 8,
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                        }}
                    >
                        <Typography variant="h5" gutterBottom sx={{
                            fontFamily: "Poppins",
                            fontSize: "24px",
                            fontWeight: "700",
                            lineHeight: "36px",
                            letterSpacing: "0.30000001192092896px",
                            textAlign: "center"
                        }}>
                            Reset your password
                        </Typography>
                        <Typography variant="body2" color="textSecondary" align="center" sx={{
                            fontFamily: "Poppins",
                            fontSize: "14px",
                            fontWeight: 400,
                            lineHeight: "19px",
                            letterSpacing: "0.30000001192092896px",
                            textAlign: "center",
                            color: "#222222"
                        }}>
                            Enter your email address and we’ll send you instructions to reset your password
                        </Typography>
                        <Box sx={{ width: '100%', mt: 3 }}>
                            <Typography variant="body2" sx={{
                                fontFamily: "Poppins",
                                fontSize: "16px",
                                fontWeight: 400,
                                lineHeight: "23px",
                                color: "#444444",
                                mb: -3,
                                textAlign: "left",
                                marginLeft: "12px"
                            }}>
                                Email
                            </Typography>
                            <TextField
                                margin="normal"
                                required
                                fullWidth
                                id="email"
                                name="email"
                                autoComplete="email"
                                value={email}
                                onChange={handleEmailChange}
                                autoFocus
                                error={!!Errorfiled} 
                                helperText={Errorfiled} 
                                sx={{
                                    width: "Fill (325px)px",
                                    height: "Hug (48px)px",
                                    padding: "12px 0px 0px 0px",
                                    borderRadius: "6px 0px 0px 0px",
                                    border: "1px 0px 0px 0px",
                                    opacity: "0px",
                                    '& .MuiOutlinedInput-notchedOutline': {
                                        borderRadius: "15px",
                                    },
                                }}
                            />
                        </Box>
                        <Button
                            type="submit"
                            fullWidth
                            variant="contained"
                            sx={{
                                width: "Fill (325px)px",
                                height: "Hug (44px)px",
                                padding: "10px 12px 10px 12px",
                                gap: "4px",
                                borderRadius: "15px",
                                opacity: "0px",
                                background: "#5F9EA0"
                            }}
                        >
                            Continue
                        </Button>

                        <Link href = {process.env.REACT_APP_IDS_API} variant="body2" align="center" sx={{
                            marginTop: 4,
                            color: '#6BB2A1',
                            textDecoration: 'none', 
                            fontFamily: "Poppins",
                            fontSize: "16px",
                            fontWeight: 400,
                            lineHeight: "24px",
                            textAlign: "center"
                        }}>
                            Back to login
                        </Link>
                        <Box mt={5}>
                            <Typography variant="body2" color="textSecondary" align="center" sx={{ marginTop: 9 }} >
                                <Link sx={{
                                    width: "Fill (325px)px",
                                    height: "Hug (24px)px",
                                    gap: "10px",
                                    opacity: "0px",
                                    textDecoration: 'none',
                                    color: '#6BB2A1',
                                    marginTop: 9,
                                    fontFamily: "Poppins",
                                    fontSize: "16px",
                                    fontWeight: 400,
                                    lineHeight: "24px",
                                    textAlign: "center"

                                }} href="/terms-of-use">Terms of use</Link> | <Link sx={{
                                    width: "Fill (325px)px",
                                    height: "Hug (24px)px",
                                    gap: "10px",
                                    opacity: "0px",
                                    textDecoration: 'none',
                                    color: '#6BB2A1',
                                    marginTop: 9,
                                    fontFamily: "Poppins",
                                    fontSize: "16px",
                                    fontWeight: 400,
                                    lineHeight: "24px",
                                    textAlign: "center"

                                }} href="/privacy-policy">Privacy Policy</Link>
                            </Typography>
                        </Box>
                    </Box>
                </form>
            ) : (
                    <Container maxWidth="xs" sx={{ marginTop: "135px" }}>
                <Box
                    sx={{
                        marginTop: 8,
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                    }}
                >
                    <Typography variant="h5" sx={{
                        fontFamily: "Poppins",
                        fontSize: "24px",
                        fontWeight: 700,
                        lineHeight: "36px",
                        letterSpacing: "0.30000001192092896px",
                        textAlign: "center",

                    }}>
                        Check your email
                    </Typography>
                    <Typography variant="body2" color="textSecondary" align="center" sx={{
                        fontFamily: "Poppins",
                        fontSize: "14px",
                        fontWeight: 400,
                        lineHeight: "19px",
                        letterSpacing: "0.30000001192092896px",
                        textAlign: "center"
                    }}>
                                Please check your email address {email} for instructions to reset your password.
                    </Typography>
                    <Button
                                fullWidth
                                variant="contained"
                                sx={{
                                    width: "Fill (325px)px",
                                    height: "Hug (44px)px",
                                    padding: "10px 12px 10px 12px",
                                    gap: "4px",
                                    borderRadius: "15px",
                                    background: "#5F9EA0",
                                    marginTop: 3
                                }}
                                onClick={handleSubmit}
                    >
                        Resend Email
                    </Button>
                    <Box mt={5}>
                        <Typography variant="body2" color="textSecondary" align="center" sx={{ marginTop: 9 }} >
                            <Link sx={{
                                width: "Fill (325px)px",
                                height: "Hug (24px)px",
                                gap: "10px",
                                opacity: "0px",
                                textDecoration: 'none',
                                color: '#6BB2A1',
                                marginTop: 9,
                                fontFamily: "Poppins",
                                fontSize: "16px",
                                fontWeight: 400,
                                lineHeight: "273px",
                                textAlign: "center"

                            }} href="/terms-of-use">Terms of use</Link> | <Link sx={{
                                width: "Fill (325px)px",
                                height: "Hug (24px)px",
                                gap: "10px",
                                opacity: "0px",
                                textDecoration: 'none',
                                color: '#6BB2A1',
                                marginTop: 9,
                                fontFamily: "Poppins",
                                fontSize: "16px",
                                fontWeight: 400,
                                lineHeight: "273px",
                                textAlign: "center"

                            }} href="/privacy-policy">Privacy Policy</Link>
                        </Typography>
                    </Box>
                </Box>
                        </Container>
            )}
        </Container >
    );
}

export default ForgotPassword;
