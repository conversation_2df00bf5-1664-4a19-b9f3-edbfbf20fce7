import React, { useState, useEffect, useRef } from "react";
import CreateAccount from "../account/AccountCreate"; // Ensure the file './AccountCreate.tsx' exists in the same directory
import {
	GridColDef,
	GridRenderCellParams,
	GridToolbarContainer,
	GridToolbarColumnsButton,
	GridToolbarFilterButton,
	GridToolbarDensitySelector,
	DataGrid,
	GridPaginationModel,
	GridColumnMenuProps,
} from "@mui/x-data-grid";
import { styled } from '@mui/material/styles';
import HistoryIcon from '@mui/icons-material/History';
import { Button, Menu, MenuItem, FormControlLabel, IconButton, Snackbar, Alert, Box, TextField,	Container, Tooltip, FormControl, Typography } from "@mui/material";
import { useNavigate } from "react-router-dom";
import BorderColorOutlinedIcon from "@mui/icons-material/BorderColorOutlined";
import AssignmentOutlinedIcon from '@mui/icons-material/AssignmentOutlined';
import DeleteIcon from "@mui/icons-material/Delete";
import MailIcon from "@mui/icons-material/Mail";
import SaveAltIcon from "@mui/icons-material/SaveAlt";
import loader from "../../assets/loader.gif";
import CustomGrid from "../common/Grid";
import MarkEmailReadIcon from "@mui/icons-material/MarkEmailRead";
import VisibilityIcon from "@mui/icons-material/Visibility";
import Card from "../common/Card";
import ModernButton from "../common/ModernButton";
import ModernDataGrid from "../common/ModernDataGrid";
//import { AccountData } from "./AccountData";
import EditAccount from "../account/AccountEdit";
import { GetAllAccounts, fetchDeleteAccountDetails, GetAccountsList, fetchAccountsById, UpdateAccountOpenAiKey } from "../../services/AccountService";
import { GetSystemPromptsList } from "../../services/SystemPromtServices";
import { isSidebarOpen, subscribe } from "../adminMenu/sidemenustate";
import { organizationsList } from "../organization/orgData";
import CustomColumnMenu from "../CustomColumnMenu";
import AccountCustomColumnMenu from "../account/AccountsColumnMenu";
import { useSnackbar } from "../../SnackbarContext";
import styles from "../account/AccountStle.module.scss";
// import { OrganizationId } from "../common/Home";
import { OrganizationId } from "../common/Home";
import FilterPopup from "../settings/Filterpopup";
import InputAdornment from "@mui/material/InputAdornment";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import { useTranslation } from "react-i18next";
import AddBoxIcon from '@mui/icons-material/AddBox';
import Delete from "../../assets/icons/delete.svg";
import { accountId } from "../adminMenu/AdminMenu";
import { useAuth } from "../auth/AuthProvider";
import JSEncrypt from "jsencrypt";
import { ConstructionOutlined } from "@mui/icons-material";
import { key, openaikey } from "../../assets/icons/icons";

// Styled components for modern design
const ModernContainer = styled(Container)({
  padding: 'var(--spacing-6)',
  maxWidth: '100%',
  backgroundColor: 'var(--color-gray-50)',
  minHeight: '100vh',
});

const HeaderSection = styled(Box)({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'flex-start',
  marginBottom: 'var(--spacing-6)',
  gap: 'var(--spacing-4)',
});

const PageTitle = styled(Typography)({
  fontSize: 'var(--font-size-2xl)',
  fontWeight: 'var(--font-weight-bold)',
  color: 'var(--color-gray-900)',
  margin: 0,
});

const PageDescription = styled(Typography)({
  fontSize: 'var(--font-size-sm)',
  color: 'var(--color-gray-600)',
  marginTop: 'var(--spacing-1)',
});

const ActionSection = styled(Box)({
  display: 'flex',
  gap: 'var(--spacing-3)',
  alignItems: 'center',
});



const ActionButton = styled(IconButton)({
  padding: 'var(--spacing-2)',
  borderRadius: 'var(--radius-md)',
  border: '1px solid var(--color-gray-200)',
  backgroundColor: 'var(--color-white)',
  transition: 'var(--transition-fast)',
  '&:hover': {
    backgroundColor: 'var(--color-gray-50)',
    borderColor: 'var(--color-gray-300)',
  },
});

const LoadingContainer = styled(Box)({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  padding: 'var(--spacing-8)',
  backgroundColor: 'var(--color-white)',
  borderRadius: 'var(--radius-lg)',
  border: '1px solid var(--color-gray-200)',
});

type InputFields = {
	OpenAIKey: string;
};
type ErrorFields = Partial<InputFields>;

interface Model {
	AccountId: string;
	BotName: string;
	CreatedBy: string;
	CreatedDate: string;
	Id: string;
	OrganizationId: string;
	SystemPrompt: string;
	UpdatedBy: string;
	UpdatedDate: string;
	OpenAIKey: string;
}

interface CurrentAccountDetails {
	AccountId: string;
	AccountName: string;
	OrganizationId: string;
}
// let AccountIds = "********-*********-e99c7653-d095-46a8-aef4-7008f293d33f";
// let OrganizationIds = "********-*********-df49ef77-08c7-4d7b-b085-06255181b55a";

const AgentsList = () => {
	const { t: translate } = useTranslation();
	const navigate = useNavigate();
	const { signOut, userDetails } = useAuth();
	const ORGANIZATION_ID = userDetails?.OrganizationId;
	console.log("ORGANIZATION_ID", ORGANIZATION_ID);
	const AccountId = localStorage.getItem("CurrentAccountId");
	const [showPopup, setShowPopup] = useState(false);
	const [models, setModels] = useState<Model[]>([]);
	const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
	const [loading, setLoading] = useState(false);
	const [accountidedit, setAccountIdEdit] = useState("");
	const [showeditPopup, setShowEditPopup] = useState(false);
	const [emailiddelete, setemailiddelete] = useState("");
	const [showDeletePopup, setShowDeletePopup] = useState(false);
	const [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());
	const [snackbarOpen, setSnackbarOpen] = useState(false);
	const [snackbarMessage, setSnackbarMessage] = useState("");
	const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error">("success");
	const storedOrganizationString = localStorage.getItem("organization");
	const storedOrganization = storedOrganizationString ? JSON.parse(storedOrganizationString) : null;
	const Organizationid = OrganizationId;
	const [skip, setskip] = useState(0);
	const [top, settop] = useState(0);
	const [totalcount, setTotalcount] = useState(0);
	const [searchText, setSearchText] = useState("");
	const { openSnackbar } = useSnackbar();
	const [orderByFields, setOrderByFields] = useState("");
	const [currentAccount, setCurrentAccount] = useState<CurrentAccountDetails>();
	const [sortModel, setSortModel] = useState([]);
	const [filters, setFilters] = useState([]);
	const handleSortModelChange = (model: any) => {
		setSortModel(model);
		const orderByField = model
			.map((item: any) => (item.sort === "desc" ? `${item.field} desc` : item.field))
			.join(", ");

		setOrderByFields(orderByField);
		GetSystemPromptsList(setModels, setLoading, ORGANIZATION_ID,AccountId, skip, top, setTotalcount, orderByField, filters);
	};
	const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({
		page: 0,
		pageSize: 15,
	});
	const [errors, setErrors] = useState<ErrorFields>({
		OpenAIKey: "",
	});
	const [accountOpenAiKey, setAccountOpenAiKey] = useState("");
	const [isOpenAiKeyValueChanges, setOpenAiKeyValueChanged] = useState(false);
	const [showInput, setShowInput] = useState(false);
	const [isOpenAiKeyValid, setOpenAiKeyValid] = useState(false);
	const [newOpenAiKey, setNewOpenAiKey] = useState("");
	const [isOpenAiKeyUpdatedSuccessfully, setOpenAiKeyUpdatedSuccesfully] = useState(false);

	const OpenAIKeyUpdatedSuccesFully = () => {
		setOpenAiKeyValueChanged(false);
		setShowInput(false);
		setNewOpenAiKey("");
		setOpenAiKeyUpdatedSuccesfully(true);
	}

  

	const FetchSystemPrompts = () => {
		setLoading(true);
		const skipcount = paginationModel.pageSize || 15;
		const limitcount = paginationModel.page * skipcount;
		const skips = limitcount;
		const top = paginationModel.pageSize;
		setskip(skips);
		settop(top);

		// Custom function to handle the API response
		const handleSystemPromptResponse = (data: any) => {
			// Check if data is a single object (not an array)
			if (data && !Array.isArray(data) && typeof data === 'object') {
				// Wrap the single object in an array
				setModels([data]);
				// Set total count to 1 since we have only one item
				setTotalcount(1);
			} else if (Array.isArray(data)) {
				// If it's already an array, use it as is
				setModels(data);
				setTotalcount(data.length);
			} else {
				// Handle empty or invalid data
				setModels([]);
				setTotalcount(0);
			}
			setLoading(false);
		};

		GetSystemPromptsList(handleSystemPromptResponse, setLoading, ORGANIZATION_ID, AccountId, skips, top, setTotalcount, orderByFields, filters);
	};

	const FetchAccountById = async () => {
		var account = await fetchAccountsById(AccountId);
		console.log("current Account", account);
		setAccountOpenAiKey(account?.OpenAIKey);
		setCurrentAccount(account);
		setOpenAiKeyValueChanged(false);
		setShowInput(false);
		setNewOpenAiKey("");
		setOpenAiKeyUpdatedSuccesfully(false);
		setErrors({ OpenAIKey: "" });
	}

	useEffect(() => {
		setLoading(true);
		FetchAccountById();
		FetchSystemPrompts();
	}, [paginationModel,accountId]);

	useEffect(() => {
		if (models.length !== 0) {
			setLoading(false);
		}
	}, [models]);

	useEffect(() => {
		const unsubscribe = subscribe(setSidebarOpen);
		return () => unsubscribe();
	}, []);

	const handleSnackbarClose = () => {
		setSnackbarOpen(false);
	};

	const columns: GridColDef[] = [
		{
			field: "BotName",
			headerName: translate('Bot Name'),
			width: 200,
			flex: 1,
			disableColumnMenu: true,
			resizable: false,
        },
        {
			field: "SystemPrompt",
			headerName: translate('System Prompt'),
			width: 350,
			flex: 2,
			disableColumnMenu: true,
			resizable: false,
			renderCell: (params) => {
				const systemPrompt = params.value || '';
				const truncatedText = systemPrompt.length > 50 ? `${systemPrompt.substring(0, 50)}...` : systemPrompt;

				return (
					<Tooltip title={systemPrompt} arrow>
					<div
					  style={{
						whiteSpace: 'nowrap',
						overflow: 'hidden',
						textOverflow: 'ellipsis',
						width: '100%' // Ensures it takes the full cell width
					  }}
					>
					  {systemPrompt}
					</div>
				  </Tooltip>
				);
			}
		},
		{
			field: "CreatedBy",
			headerName: translate('Created By'),
			width: 150,
			flex: 1,
			disableColumnMenu: true,
			resizable: false,
		},
		{
  field: "CreatedDate",
  headerName: translate('Created Date'),
  width: 150,
  flex: 1,
  disableColumnMenu: true,
  renderCell: (params) => {
    const dateUtc = params.value ? new Date(params.value) : null;
    if (!dateUtc) return "";

    // Convert to local timezone explicitly
    const localDate = new Date(dateUtc.getTime() + dateUtc.getTimezoneOffset() * 60000);

    return localDate.toLocaleString(undefined, {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: true, // Set false for 24-hr format
    });
  },
  resizable: false,
},

		{
			field: "UpdatedDate",
			headerName: translate('Updated Date'),
			width: 150,
			flex: 1,
			disableColumnMenu: true,
			renderCell: (params) => {
				const updatedDate = params.value ? new Date(params.value) : null;
				return updatedDate ? updatedDate.toLocaleDateString('en-GB') : "";
			},
			resizable: false,
		},
		{
			field: "actions",
			headerName: translate('Actions'),
			sortable: false,
			width: 150,
			flex: 1,
			disableColumnMenu: true,
			resizable: false,
			renderCell: (params) => {
				var emailId = params.row.CreatedBy || false;
				const accountid = params.row.AccountId || false;
				return (
					<Box sx={{ display: 'flex', gap: 1 }}>
						<Tooltip arrow title={translate('View to Edit')}>
							<ActionButton
								size="small"
								onClick={() => navigate("/settings/scripts", { state: { agentData: params.row } })}
							>
								<BorderColorOutlinedIcon fontSize="small" />
							</ActionButton>
						</Tooltip>
						<Tooltip arrow title={translate('System Prompt History')}>
							<ActionButton
								size="small"
								onClick={() => navigate("/settings/scripthistory", { state: { agentData: params.row } })}
							>
								<HistoryIcon fontSize="small" />
							</ActionButton>
						</Tooltip>
					</Box>
				);
			},
		},
	];

	const onPageChange = (newPage: number) => {
	};
	const onPageSizeChange = (newPageSize: number) => {
	};

	const CustomToolbar: React.FC<any> = () => {
		const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
		

		const handleExportMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {
			setAnchorEl(event.currentTarget);
		};

		const handleExportMenuClose = () => {
			setAnchorEl(null);
		};

		const handleDownloadExcelClick = () => {
			handleExportMenuClose();
		};

		return (
			<div style={{ display: "flex", alignItems: "center" }}>
				<GridToolbarContainer>
					<GridToolbarColumnsButton />
					<GridToolbarFilterButton />
					<GridToolbarDensitySelector />
				</GridToolbarContainer>
				<Button
					aria-controls="export-menu"
					aria-haspopup="true"
					onClick={handleExportMenuClick}
					style={{ marginLeft: "10px" }}
					startIcon={<SaveAltIcon />}
				>
					Export
				</Button>
				<Menu
					id="export-menu"
					anchorEl={anchorEl}
					keepMounted
					open={Boolean(anchorEl)}
					onClose={handleExportMenuClose}
				>
					<MenuItem
						onClick={() => {
							//handleDownloadExcel();
							handleExportMenuClose();
						}}
					>
						Download Excel
					</MenuItem>
				</Menu>
			</div>
		);
	};


	const openPopup = () => {
		setShowPopup(true);
	};
	const handleSearch = (value: string[]) => {
		setSearchText(value.join(" "));
	};

	const globalhandleSearch = (searchValue: any) => {

		try {
			// Use the same handler function as in FetchSystemPrompts
			const handleSystemPromptResponse = (data: any) => {
				// Check if data is a single object (not an array)
				if (data && !Array.isArray(data) && typeof data === 'object') {
					// If search value matches the BotName, show it, otherwise show empty array
					if (data.BotName.toLowerCase().includes(searchValue.toLowerCase())) {
						setModels([data]);
						setTotalcount(1);
					} else {
						setModels([]);
						setTotalcount(0);
					}
				} else if (Array.isArray(data)) {
					// If it's already an array, filter it
					const filteredData = data.filter(item =>
						item.BotName.toLowerCase().includes(searchValue.toLowerCase())
					);
					setModels(filteredData);
					setTotalcount(filteredData.length);
				} else {
					// Handle empty or invalid data
					setModels([]);
					setTotalcount(0);
				}
				setLoading(false);
			};

			GetSystemPromptsList(handleSystemPromptResponse, setLoading, ORGANIZATION_ID, AccountId);
		} catch (error) {
			console.error("Failed to fetch system prompts:", error);
		}
	};

	const onOpenAiKeyChange = (event:any) => {
		//console.log(accountOpenAiKey);
		const value = event.target.value;
		setNewOpenAiKey(value);
		setOpenAiKeyValueChanged(true);
		if (value === ''||!isValidOpenAIKeyFormat(value)) {
			setOpenAiKeyValid(false);
		} else {
			setOpenAiKeyValid(true);
		}
		
	}

	const updateOpenAiKey = () => {
		
		const newAccountDetails = { ...currentAccount, OpenAIKey: newOpenAiKey };//Need to replace with OpenAi Key
		UpdateAccountOpenAiKey(setLoading, newAccountDetails, openSnackbar,OpenAIKeyUpdatedSuccesFully,setErrors,setOpenAiKeyValid);
		//Update OpenAi Key 
	}

	

	const isValidOpenAIKeyFormat = (key: string) => {
		return /^sk-[a-zA-Z0-9_-]{20,}$/.test(key);
	};
	
	const handleKeyDown = (event: { key: string; }) => {
		if (event.key === 'Enter') {
			if (newOpenAiKey != '' && isValidOpenAIKeyFormat(newOpenAiKey)) {
				setOpenAiKeyValid(true);
				updateOpenAiKey();
			}
			else {
				
			}
		}
	};
	
	// Removed unused variables and functions

	return (
		<ModernContainer>
			{/* Header Section */}
			<HeaderSection>
				<Box>
					<PageTitle>{translate('System Prompts')}</PageTitle>
					<PageDescription>{translate('View and manage your system prompts')}</PageDescription>
				</Box>

				<ActionSection>
					{/* OpenAI Key Section */}
					<div
						className={
							`open-ai-key-wrapper ${showInput
								? `expanded ${isOpenAiKeyValueChanges && !isOpenAiKeyValid ? 'open-ai-key-error' : ''}`
								: ''} ${isOpenAiKeyUpdatedSuccessfully ? 'open-ai-key-submitted' : ''}`}
					>
						<div className="open-ai-key-out">
							<div className="open-ai-key-box">
								{showInput ? (
									<div className="open-ai-key-input-wrapper">
										<span className="input-icon" onClick={() => setShowInput(false)}>
											<img src={key} alt="OpenAI Key" />
										</span>
										<input
											type="password"
											placeholder="Enter OpenAI API Key (Eg: sk-affjg..)"
											className="open-ai-key-input"
											onKeyDown={handleKeyDown}
											onChange={onOpenAiKeyChange}
										/>
									</div>
								) : (
									<div className="open-ai-key-box-icon" onClick={() => setShowInput(true)}>
										<Tooltip
											arrow
											title={accountOpenAiKey == "" && errors.OpenAIKey == ""
												? translate('OpenAI Key Not Provided')
												: errors.OpenAIKey == "" ?
													translate(`sk-....${(accountOpenAiKey || "").slice(-4)}`)
													: translate(errors?.OpenAIKey||"")}
										>
											<img src={key} alt="OpenAI Key" />
										</Tooltip>
									</div>
								)}
							</div>
						</div>
					</div>

					{/* Create Prompt Button */}
					<Tooltip arrow title={translate('Coming soon')}>
						<span>
							<ModernButton
								variant="primary"
								disabled
								startIcon={<AddBoxIcon />}
							>
								{translate('Create Prompt')}
							</ModernButton>
						</span>
					</Tooltip>
				</ActionSection>
			</HeaderSection>
			{/* Popup for Create Account */}
			{showPopup && (
				<CreateAccount
					setModels={setModels}
					setLoading={setLoading}
					showPopup={showPopup}
					setShowPopup={setShowPopup}
					orderByField={orderByFields}
				/>
			)}

			{/* Main Content Card */}
			<Card padding="none" shadow="md">
				{models.length === 0 ? (
					<LoadingContainer>
						<img
							src={loader}
							alt="Loading..."
							style={{ width: '40px', height: '40px' }}
						/>
					</LoadingContainer>
				) : (
					<ModernDataGrid
						rows={models}
						columns={columns}
						getRowId={(row) => row.Id || row.AccountId}
						loading={models.length === 0}
					/>
				)}
			</Card>

			{/* Snackbar for notifications */}
			<Snackbar
				open={snackbarOpen}
				autoHideDuration={4000}
				onClose={handleSnackbarClose}
				anchorOrigin={{ vertical: "top", horizontal: "right" }}
			>
				<Alert
					onClose={handleSnackbarClose}
					severity={snackbarSeverity}
				>
					{snackbarMessage}
				</Alert>
			</Snackbar>
		</ModernContainer>
);

};

export default AgentsList;