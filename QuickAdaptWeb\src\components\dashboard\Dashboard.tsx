  
  
import React, { useState } from 'react';

import { isSidebarOpen } from "../adminMenu/sidemenustate";
import { Container } from '@mui/material';

const Dashboard = () => {
  const [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());
  return (
    <Container maxWidth="xl">
   
       <div className={`smooth-transition`} style={{marginLeft:sidebarOpen?"170px":""}}>
        <center><h1>Dashboard</h1></center>
      </div>
   
      </Container>
    );
  };
  

export default Dashboard