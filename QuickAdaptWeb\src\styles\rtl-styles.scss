.rtl {
	.qadpt-banner {
		direction: rtl !important;
		.adapat-banner-right {
			right: auto !important;
			left: 9px;
		}
	}
	.qadpt-page-content {
		.qadpt-side-menu .qadpt-smenu .MuiDrawer-paper {
			direction: rtl;
			float: right;
			right: 0px;
			border-right: 0;
			border-left: 1px solid var(--border-color);
	
.qadpt-smenu-list{
	.qadpt-sm-icon img[alt="Banners"],
.qadpt-sm-icon img[alt="Tooltips"],
.qadpt-sm-icon img[alt="Surveys"] {
  transform: scaleX(-1);
}
}
		}
		.qadpt-settings-content {
			left: 0 !important;
			right: 240px;
		}
	}
	.qadpt-orgcont {
		right: 20px;
		left: auto;
	}
	.qadpt-homepg,
	.qadpt-web {
		left: auto;
		right: 220px;
		.qadpt-webcontent{
			.qadpt-setting-title {
			.qadpt-back-text{
				right: 10px;
				left: 0 !important;
			}
			.qadpt-titsec .qadpt-action-btn .MuiButton-icon{
				margin-left: 8px !important;
    			margin-right: -4px !important;
			}
		}
		.qadpt-set-right {
			.qadpt-page-target .qadpt-header-container .qadpt-add-target-btn.save{
			margin-right: 10px;
			margin-left: 0 !important;
		}
		.qadpt-rev-publish{ 
			.MuiCardContent-root .MuiGrid-root .MuiGrid-item{
				padding-right: 16px;
				padding-left: 0 !important;
			}
			.MuiCardContent-root .MuiGrid-root .MuiGrid-item:first-of-type {
			padding-right: 0 !important;
			padding-left: 0 !important;
			}
			.qadpt-gridleft{
			border-left: 1px solid #ccc;
			border-right: 0 !important;
		}
	}
	.qadpt-frequency .qadpt-card fieldset .MuiGrid-root .MuiGrid-item:nth-child(4) label{
		margin-right: 0px !important; 
    margin-left: 16px !important;
	}
		}
		}
	}

	.css-5kysay-MuiDataGrid-root .MuiDataGrid-cell--textLeft {
		text-align: right;
	}
	.MuiTablePagination-actions {
		.MuiButtonBase-root .MuiSvgIcon-root {
			transform: rotate(180deg) !important;
		}
	}
	.qadpt-orgcont .qadpt-head .qadpt-right-part {
		text-align: left;
	}
	.qadpt-createpopup {
		.qadpt-closeicon {
			left: 15px;
			right: auto;
		}
	}
	.MuiDataGrid-scrollbar.MuiDataGrid-scrollbar--vertical {
		right: auto;
		left: 0;
	}
	.qadpt-page-content .qadpt-side-menu {
		right: 10px;
		left: auto;
	}
	.qadpt-feedbackpopup {
		.character-count {
			text-align: left !important;
		}
		.qadpt-upload-button {
			.css-1d6wzja-MuiButton-startIcon {
				margin: 0 8px;
			}
		}
		.qadpt-rating-stars.css-1qqgbpl-MuiRating-root {
			text-align: right !important;
		}
		.qadpt-feedback-header .qadpt-close-icon {
			left: 15px;
			right: auto !important;
		}
	}
	.confirm-actions {
		gap: 5px;
	}
	.qadpt-prfsidebar {
		.qadpt-prftitle {
			text-align: right !important;
			float: right;
			margin-right: 10px !important;
			margin-left: auto !important;
		}
	}
	.qadpt-prfbox .qadpt-txtctrl {
		.qadpt-prffldval .MuiOutlinedInput-root input {
			text-align: right !important;
		}
		&.gender-fld {
			.css-hfutr2-MuiSvgIcon-root-MuiSelect-icon {
				right: auto !important;
				left: 8px;
			}
		}
	}
	.qadpt-crossIcon {
		float: left !important;
	}
	.css-nswfot-MuiDialogActions-root {
		gap: 5px !important;
	}
.qadpt-midpart .qadpt-content-block {
	.slt-acc-drp,.qadpt-filter-left .qadpt-select-form:nth-of-type(2){
		fieldset{
				text-align: right !important;
			}
			 .MuiInputLabel-root {
      right: 21px;
    	left: auto;
		 &:not(.qadpt-filter-left .qadpt-select-form:nth-of-type(2) .MuiInputLabel-root).MuiInputLabel-shrink {
      right: -4px !important;
    }
      }
	}
	.qadpt-head .qadpt-right-part {
		text-align: left !important;
	}


	.qadpt-content-box .qadpt-src-flt .qadpt-teamsearch {
		right: auto !important;
		left: 10px;
	}


	.qadpt-account-grd {
		.MuiDataGrid-root .MuiChip-deleteIcon {
			margin: 0 -6px 0 5px !important;

		}
	}

	.grid-toolbar-options .left-options {

		.drp-fields,
		.dt-fields {
			margin-left: 15px;
			margin-right: 0 !important;
		}
		.drp-fields{
			.MuiAutocomplete-endAdornment{
				      left: 9px;
    right: auto;
			}
		}
		 .drp-fields, .dt-fields ,.name-fld{
			fieldset{
				text-align: right !important;
			}
			 .MuiInputLabel-root {
      right: 21px;
    	left: auto;
		&.MuiInputLabel-shrink{
			right: 10px !important;
		}
      }
	}

		.qadpt-DateTime.dt-fld2 {
			margin-left: 15px;
			margin-right: 0 !important;
		}
		}
								}
				
								.MuiSelect-icon {
									right: auto;
									left: 7px;
								}
	
.qadpt-closeicon{
    left: 15px;
	right : auto !important
}
.qadpt-mngpwd-popup .MuiDialogActions-root button:last-child{
	margin-right: 8px;
	margin-left: 0 !important;

}
.qadpt-trainpop .qadpt-uplddoc{
	.MuiButton-icon{
		margin-left: 8px !important;
   	 margin-right: 0px !important;
	}
}
.qadpt-usercreatepopup .qadpt-formcontent .qadpt-usrname div:nth-child(2){
	    margin-right: 10px;
		margin-left: 0 !important;
}
.qadpt-roleeditpopup .qadpt-addrole .MuiGrid-root .MuiGrid-item .MuiFormControl-root{
		fieldset{
				text-align: right !important;
			}
			 .MuiInputLabel-root {
      right: 21px;
    	left: auto;
		 &.MuiInputLabel-shrink {
      right: 4px !important;
    }
      }
	}
	.qadpt-roleeditpopup .qadpt-addrole .MuiGrid-root .MuiGrid-item:last-child .MuiFormControl-root .MuiInputLabel-root.MuiInputLabel-shrink {
  right: 18px !important;
	}
	.qadpt-rolesfltpopup .MuiDialog-container{
	.qadpt-title .qadpt-close{
		
			left : 8px;
			right: auto !important;
	}
	.MuiDialogContent-root .MuiButton-icon{
		margin-left: 8px !important;
    	margin-right: 0px !important;

	}
	}
	.qadpt-prfpopup .qadpt-logout img{
		transform: scaleX(-1);
	}

								}