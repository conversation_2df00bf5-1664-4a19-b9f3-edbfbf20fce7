import React from 'react';
import { FormControl, InputLabel, Select, MenuItem, FormHelperText, SelectProps } from '@mui/material';
import { styled } from '@mui/material/styles';

interface ModernSelectProps extends Omit<SelectProps, 'variant'> {
  label?: string;
  helperText?: string;
  error?: boolean;
  options: Array<{ value: string | number; label: string; disabled?: boolean }>;
  variant?: 'outlined' | 'filled';
}

const StyledFormControl = styled(FormControl)({
  '& .MuiInputLabel-root': {
    fontSize: 'var(--font-size-sm)',
    fontWeight: 'var(--font-weight-medium)',
    color: 'var(--color-gray-700)',
    
    '&.Mui-focused': {
      color: 'var(--color-primary-600)',
    },
    
    '&.Mui-error': {
      color: 'var(--color-error-600)',
    },
  },
  
  '& .MuiFormHelperText-root': {
    fontSize: 'var(--font-size-xs)',
    marginTop: 'var(--spacing-1)',
    
    '&.Mui-error': {
      color: 'var(--color-error-600)',
    },
  },
});

const StyledSelect = styled(Select)({
  borderRadius: 'var(--radius-md)',
  backgroundColor: 'var(--color-white)',
  fontSize: 'var(--font-size-sm)',
  transition: 'var(--transition-fast)',
  
  '& .MuiOutlinedInput-notchedOutline': {
    borderColor: 'var(--color-gray-300)',
    borderWidth: '1px',
  },
  
  '&:hover .MuiOutlinedInput-notchedOutline': {
    borderColor: 'var(--color-gray-400)',
  },
  
  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
    borderColor: 'var(--color-primary-500)',
    borderWidth: '2px',
  },
  
  '&.Mui-error .MuiOutlinedInput-notchedOutline': {
    borderColor: 'var(--color-error-500)',
  },
  
  '&.Mui-disabled': {
    backgroundColor: 'var(--color-gray-50)',
    '& .MuiOutlinedInput-notchedOutline': {
      borderColor: 'var(--color-gray-200)',
    },
  },
  
  '& .MuiSelect-select': {
    padding: 'var(--spacing-3) var(--spacing-4)',
    fontSize: 'var(--font-size-sm)',
    color: 'var(--color-gray-900)',
    
    '&.Mui-disabled': {
      color: 'var(--color-gray-500)',
    },
  },
  
  '& .MuiSelect-icon': {
    color: 'var(--color-gray-500)',
    
    '&.Mui-disabled': {
      color: 'var(--color-gray-300)',
    },
  },
});

const StyledMenuItem = styled(MenuItem)({
  fontSize: 'var(--font-size-sm)',
  padding: 'var(--spacing-2) var(--spacing-4)',
  color: 'var(--color-gray-900)',
  
  '&:hover': {
    backgroundColor: 'var(--color-gray-50)',
  },
  
  '&.Mui-selected': {
    backgroundColor: 'var(--color-primary-50)',
    color: 'var(--color-primary-700)',
    
    '&:hover': {
      backgroundColor: 'var(--color-primary-100)',
    },
  },
  
  '&.Mui-disabled': {
    color: 'var(--color-gray-400)',
  },
});

const ModernSelect: React.FC<ModernSelectProps> = ({
  label,
  helperText,
  error = false,
  options,
  variant = 'outlined',
  fullWidth = true,
  ...props
}) => {
  return (
    <StyledFormControl fullWidth={fullWidth} error={error}>
      {label && <InputLabel>{label}</InputLabel>}
      <StyledSelect
        variant={variant}
        label={label}
        {...props}
      >
        {options.map((option) => (
          <StyledMenuItem
            key={option.value}
            value={option.value}
            disabled={option.disabled}
          >
            {option.label}
          </StyledMenuItem>
        ))}
      </StyledSelect>
      {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </StyledFormControl>
  );
};

export default ModernSelect;
