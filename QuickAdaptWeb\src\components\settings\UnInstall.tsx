import React, { useState } from 'react';
import { Container, Box, Typography, TextField, Button, Link, IconButton, InputAdornment } from '@mui/material';
import { feedbackimg, QuickAdopttext, Refresh } from "../../assets/icons/icons";
const Cleanup: React.FC = () => {
  const [feedback, setFeedback] = useState('');
  const [thankYouMessage, setThankYouMessage] = useState(false); // State to control the thank you message

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    localStorage.clear();
    sessionStorage.clear();
    document.cookie.split(";").forEach(function(c) {
      document.cookie = c
        .replace(/^ +/, "")
        .replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
    });

    setThankYouMessage(true);
    setTimeout(() => {
      window.location.href = `${process.env.REACT_APP_WEB_API}`; 
    }, 2000);
  };

  return (  
        <Container maxWidth="sm" className="qadpt-unistpg">
        <Box mb={4} className="qadpt-brand-logo">
            <img 
                src={QuickAdopttext} 
                alt="QuickAdopt Logo" 
                className="qadpt-brand-logo-img"
            />
         </Box>        
     
        {!thankYouMessage && (
          <>
            <div className='qadpt-mgs'>
        <div>We're sorry to see you go.</div>
        <div>Before you leave , we'd love to hear your feedback</div>
          <div>Need the extension again ?   <img 
                  src={Refresh} 
                  alt="Refresh img" 
                  
              />  <a href = "https://chromewebstore.google.com/detail/quickadapt/bniabjfpljchdaipkcadcnpjndjdoaom">Re-Install</a> it</div>
          </div>
          <form className='qadpt-feedbkfrm'
            onSubmit={handleSubmit}
                    >
            <div>Feedback</div>
            <textarea
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              placeholder="Share your feedback"
              rows={4}
              cols={50}
            
            />
         
            <button
              type="submit"
              
              className='qadpt-btn'
              disabled={feedback.trim().length === 0}
              style={{
                      backgroundColor: '#5f9ea0',
                      pointerEvents: feedback.trim().length === 0 ? 'none' : 'auto',
                      opacity: feedback.trim().length === 0 ? 0.5 : 1,
                    }}
              onMouseOver={(e) => (e.currentTarget.style.backgroundColor = '#5f9ea0')}
              onMouseOut={(e) => (e.currentTarget.style.backgroundColor = '#5f9ea0')}
            >
              Submit Feedback
            </button>
          </form>
        </>
      )}

        {thankYouMessage && (
          <div className='qadpt-thkpg'>
           <img 
                  src={feedbackimg} 
                  alt="feedback img" 
                  
              />
        <div className='qadpt-thkmsg'>
              <div>Thanks for your feedback!</div>
              <div>It’s been shared with our team and will be considered for future improvements.</div>
            </div>
            </div>
      )}
   </Container>
        
 
  );
};

export default Cleanup;



