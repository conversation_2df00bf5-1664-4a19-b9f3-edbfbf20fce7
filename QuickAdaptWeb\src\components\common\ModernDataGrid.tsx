import React from 'react';
import { DataGrid, DataGridProps } from '@mui/x-data-grid';
import { styled } from '@mui/material/styles';
import { Box } from '@mui/material';

interface ModernDataGridProps extends DataGridProps {
  loading?: boolean;
}

const StyledDataGrid = styled(DataGrid)({
  backgroundColor: 'var(--color-white)',
  border: '1px solid var(--color-gray-200)',
  borderRadius: 'var(--radius-lg)',
  fontSize: 'var(--font-size-sm)',
  
  // Header styling
  '& .MuiDataGrid-columnHeaders': {
    backgroundColor: 'var(--color-gray-50)',
    borderBottom: '1px solid var(--color-gray-200)',
    borderRadius: 'var(--radius-lg) var(--radius-lg) 0 0',
    
    '& .MuiDataGrid-columnHeader': {
      fontSize: 'var(--font-size-sm)',
      fontWeight: 'var(--font-weight-semibold)',
      color: 'var(--color-gray-700)',
      
      '&:focus, &:focus-within': {
        outline: 'none',
      },
    },
    
    '& .MuiDataGrid-columnHeaderTitle': {
      fontWeight: 'var(--font-weight-semibold)',
    },
    
    '& .MuiDataGrid-iconSeparator': {
      color: 'var(--color-gray-300)',
    },
  },
  
  // Row styling
  '& .MuiDataGrid-row': {
    borderBottom: '1px solid var(--color-gray-100)',
    transition: 'var(--transition-fast)',
    
    '&:hover': {
      backgroundColor: 'var(--color-gray-50)',
    },
    
    '&.Mui-selected': {
      backgroundColor: 'var(--color-primary-50)',
      
      '&:hover': {
        backgroundColor: 'var(--color-primary-100)',
      },
    },
    
    '&:last-child': {
      borderBottom: 'none',
    },
  },
  
  // Cell styling
  '& .MuiDataGrid-cell': {
    fontSize: 'var(--font-size-sm)',
    color: 'var(--color-gray-900)',
    padding: 'var(--spacing-3) var(--spacing-4)',
    
    '&:focus, &:focus-within': {
      outline: 'none',
    },
  },
  
  // Footer styling
  '& .MuiDataGrid-footerContainer': {
    borderTop: '1px solid var(--color-gray-200)',
    backgroundColor: 'var(--color-gray-50)',
    borderRadius: '0 0 var(--radius-lg) var(--radius-lg)',
    
    '& .MuiTablePagination-root': {
      fontSize: 'var(--font-size-sm)',
      color: 'var(--color-gray-700)',
    },
    
    '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {
      fontSize: 'var(--font-size-sm)',
      color: 'var(--color-gray-600)',
    },
    
    '& .MuiIconButton-root': {
      color: 'var(--color-gray-600)',
      
      '&:hover': {
        backgroundColor: 'var(--color-gray-100)',
      },
      
      '&.Mui-disabled': {
        color: 'var(--color-gray-300)',
      },
    },
  },
  
  // Toolbar styling
  '& .MuiDataGrid-toolbarContainer': {
    padding: 'var(--spacing-4)',
    borderBottom: '1px solid var(--color-gray-200)',
    backgroundColor: 'var(--color-white)',
    
    '& .MuiButton-root': {
      fontSize: 'var(--font-size-sm)',
      fontWeight: 'var(--font-weight-medium)',
      color: 'var(--color-gray-700)',
      textTransform: 'none',
      
      '&:hover': {
        backgroundColor: 'var(--color-gray-100)',
      },
    },
  },
  
  // Loading overlay
  '& .MuiDataGrid-overlay': {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    backdropFilter: 'blur(2px)',
  },
  
  // Scrollbar styling
  '& .MuiDataGrid-virtualScroller': {
    '&::-webkit-scrollbar': {
      width: '8px',
      height: '8px',
    },
    
    '&::-webkit-scrollbar-track': {
      backgroundColor: 'var(--color-gray-100)',
      borderRadius: 'var(--radius-sm)',
    },
    
    '&::-webkit-scrollbar-thumb': {
      backgroundColor: 'var(--color-gray-300)',
      borderRadius: 'var(--radius-sm)',
      
      '&:hover': {
        backgroundColor: 'var(--color-gray-400)',
      },
    },
  },
  
  // No rows overlay
  '& .MuiDataGrid-overlayWrapper': {
    minHeight: '200px',
  },
});

const LoadingContainer = styled(Box)({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  padding: 'var(--spacing-8)',
  backgroundColor: 'var(--color-white)',
  borderRadius: 'var(--radius-lg)',
  border: '1px solid var(--color-gray-200)',
  minHeight: '200px',
});

const ModernDataGrid: React.FC<ModernDataGridProps> = ({
  loading = false,
  ...props
}) => {
  if (loading) {
    return (
      <LoadingContainer>
        <div>Loading...</div>
      </LoadingContainer>
    );
  }

  return (
    <StyledDataGrid
      disableRowSelectionOnClick
      autoHeight
      pageSizeOptions={[10, 25, 50, 100]}
      initialState={{
        pagination: {
          paginationModel: { pageSize: 25 },
        },
      }}
      {...props}
    />
  );
};

export default ModernDataGrid;
