import React from 'react';
import { GridColumnMenu, GridColumnMenuHideItem, GridColumnMenuProps } from '@mui/x-data-grid';
import CustomColumnMenuUserItem from '../components/CustomColumnmenuuserItem';
 
const CustomColumnMenu: React.FC<GridColumnMenuProps & { options: string[]; onSearch: (value: string[]) => void }> = (
	props
) => {
	const { options, onSearch, ...other } = props;

	return (
		<GridColumnMenu
			{...other}
			slots={{
				columnMenuUserItem: (menuProps) => (
					<CustomColumnMenuUserItem
						options={options}
						onSearch={onSearch}
					/>
				),
				GridColumnMenuHideItem:null
			}}
			slotProps={{
				columnMenuUserItem: {
					displayOrder: 15,
				},
				
			}}
		/>
	);
};
 
export default CustomColumnMenu;