import React from 'react';
import { Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import { TrendingUp } from '@mui/icons-material';

interface ChartPlaceholderProps {
  title: string;
  height?: string;
  showTrend?: boolean;
}

const ChartContainer = styled(Box)<{ height: string }>(({ height }) => ({
  height,
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: 'var(--color-gray-50)',
  borderRadius: 'var(--radius-lg)',
  border: '2px dashed var(--color-gray-300)',
  position: 'relative',
  overflow: 'hidden',
}));

const Mock<PERSON>hart = styled(Box)({
  width: '100%',
  height: '60%',
  display: 'flex',
  alignItems: 'flex-end',
  justifyContent: 'center',
  gap: 'var(--spacing-1)',
  padding: 'var(--spacing-4)',
});

const MockBar = styled(Box)<{ height: number; color?: string }>(({ height, color = 'var(--color-primary-300)' }) => ({
  width: '12px',
  height: `${height}%`,
  backgroundColor: color,
  borderRadius: 'var(--radius-sm)',
  transition: 'var(--transition-fast)',
  
  '&:hover': {
    backgroundColor: 'var(--color-primary-500)',
    transform: 'scaleY(1.1)',
  },
}));

const TrendIndicator = styled(Box)({
  position: 'absolute',
  top: 'var(--spacing-3)',
  right: 'var(--spacing-3)',
  display: 'flex',
  alignItems: 'center',
  gap: 'var(--spacing-1)',
  fontSize: 'var(--font-size-xs)',
  fontWeight: 'var(--font-weight-medium)',
  color: 'var(--color-success-600)',
  backgroundColor: 'var(--color-success-50)',
  padding: 'var(--spacing-1) var(--spacing-2)',
  borderRadius: 'var(--radius-md)',
});

const ChartPlaceholder: React.FC<ChartPlaceholderProps> = ({
  title,
  height = '300px',
  showTrend = true,
}) => {
  // Generate mock data for the chart
  const mockData = Array.from({ length: 12 }, () => Math.random() * 80 + 20);

  return (
    <ChartContainer height={height}>
      {showTrend && (
        <TrendIndicator>
          <TrendingUp fontSize="small" />
          +12.5%
        </TrendIndicator>
      )}
      
      <MockChart>
        {mockData.map((value, index) => (
          <MockBar
            key={index}
            height={value}
            color={
              index === mockData.length - 1
                ? 'var(--color-primary-600)'
                : index % 3 === 0
                ? 'var(--color-primary-400)'
                : 'var(--color-primary-300)'
            }
          />
        ))}
      </MockChart>
      
      <Typography
        variant="body2"
        color="text.secondary"
        sx={{ mt: 2, textAlign: 'center' }}
      >
        {title}
      </Typography>
      
      <Typography
        variant="caption"
        color="text.secondary"
        sx={{ opacity: 0.7 }}
      >
        Interactive chart component placeholder
      </Typography>
    </ChartContainer>
  );
};

export default ChartPlaceholder;
