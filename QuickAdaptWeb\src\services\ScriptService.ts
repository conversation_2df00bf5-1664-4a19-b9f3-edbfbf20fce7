import { adminApiService } from "./APIService";

// Mock data for initial development
const mockScriptData = {
  id: "script1",
  content: `// Sample agent script
function processUserInput(input) {
  // Process user input
  console.log("Processing user input:", input);

  // Perform some analysis
  const sentiment = analyzeSentiment(input);

  // Generate response based on sentiment
  if (sentiment > 0.5) {
    return "I'm glad you're feeling positive!";
  } else if (sentiment < -0.5) {
    return "I'm sorry to hear that. How can I help?";
  } else {
    return "Thank you for your message. Is there anything specific you'd like to know?";
  }
}

function analyzeSentiment(text) {
  // This is a placeholder for sentiment analysis
  // In a real implementation, this would use NLP or call an external API
  if (text.includes("happy") || text.includes("great") || text.includes("good")) {
    return 0.8;
  } else if (text.includes("sad") || text.includes("bad") || text.includes("unhappy")) {
    return -0.8;
  } else {
    return 0;
  }
}

// Export the main function
module.exports = {
  processUserInput
};`,
};

// Get script by agent ID
export const getScriptByAgentId = async (agentId: string) => {
  try {
    // In a real implementation, this would call the API to get the system prompt
    // const response = await adminApiService.get(`/Assistant/GetSystemPromptById?id=${agentId}`);
    // return { content: response.data.SystemPrompt };

    // For now, return mock data
    console.log(`Getting system prompt for agent ID: ${agentId}`);
    return mockScriptData;
  } catch (error) {
    console.error("Error fetching system prompt:", error);
    throw error;
  }
};

// Save script for an agent
export const saveScript = async (agentId: string, scriptContent: string) => {
  try {
    // In a real implementation, this would call the API to update the system prompt
    // const response = await adminApiService.post(`/Assistant/UpdateSystemPrompt`, {
    //   Id: agentId,
    //   SystemPrompt: scriptContent
    // });
    // return response.data;

    // For now, simulate API call with a delay
    console.log(`Saving system prompt for agent ID: ${agentId}`);
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { success: true, message: "System prompt saved successfully" };
  } catch (error) {
    console.error("Error saving system prompt:", error);
    throw error;
  }
};
