export interface updateProfile {
	UserId: string,
	FirstName: string,
	LastName: string,
	UserName: string,
	EmailId: string,
	Password: string,
	ContactNumber: string,
	OrganizationId: string,
	ProfilePhoto: string,
	UserType: string,
	Gender: string,
	DateofBirth: string,
	TimeZone: string,
	IsBlocked:boolean
}
export interface GetUserDetails{


	UserId: string,
	FirstName: string,
	LastName: string,
	UserName: string,
	EmailId: string,
	Password: string,
	ContactNumber: string,
	OrganizationId:string,
	ProfilePhoto: string,
	UserType: string,
	Gender: string,
	DateofBirth: string,
	TimeZone:string,
	//RTL: false,
	//LoginType: Standard,
	//EmailConfirmed: true,
	////AdminDeactivated: false,
	//IsBlocked: false,
	//InvalidAttempts: 0
}