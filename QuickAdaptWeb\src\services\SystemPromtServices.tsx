import { adminApiService,userApiService } from './APIService';

// Interface for system prompt data
export interface SystemPromptData {
  Id: string;
  OrganizationId: string;
  AccountId: string;
  BotName: string;
  SystemPrompt: string;
  CreatedDate?: string;
  CreatedBy?: string;
  UpdatedDate?: string;
  UpdatedBy?: string;
}

export const GetSystemPromptsList = async (
	setModels: any,
	setLoading: any,
    OrganizationIds: any,
    AccountIds: any,
	skip?: any,
	top?: any,
	setTotalCount?: any,
	orderByField?: any,
    filters?: any,
) => {
	try {
		setLoading(true);
		const response = await userApiService.get(`/Assistant/GetSystemPromptByIds?organizationId=${OrganizationIds}&accountId=${AccountIds}`);
		let apiData = response.data;

		// Pass the data to the callback function
		setModels(apiData);

	} catch (error) {
        console.error("Error fetching system prompts:", error);
        throw error;
	} finally {
		// setLoading is handled by the callback function
	}
};

// Function to get a system prompt by ID
export const GetSystemPromptById = async (promptId: string) => {
	try {
		const response = await userApiService.get(`/Assistant/GetSystemPromptById?id=${promptId}`);
		return response.data;
	} catch (error) {
		console.error("Error fetching system prompt by ID:", error);
		throw error;
	}
};


// Function to update a system prompt
// export const UpdateSystemPrompt = async (systemPromptData: SystemPromptData) => {
// 	try {
// 		// Ensure the SystemPrompt field is a string
// 		const dataToSend = {
// 			...systemPromptData,
// 			SystemPrompt: typeof systemPromptData.SystemPrompt === 'string'
// 				? systemPromptData.SystemPrompt
// 				: JSON.stringify(systemPromptData.SystemPrompt)
// 		};

// 		console.log("Sending data to API:", dataToSend);

// 		const response = await userApiService.post('/Assistant/UpdateSystemPrompt', dataToSend,{
// 			headers: {
// 			  'Content-Type': 'application/json'
// 			}
// 		  });
// 		console.log("API response:", response.data);

// 		return {
// 			success: true,
// 			data: response.data,
// 			message: "System prompt updated successfully"
// 		};
// 	} catch (error) {
// 		console.error("Error updating system prompt:", error);
// 		return {
// 			success: false,
// 			error: error,
// 			message: "Failed to update system prompt"
// 		};
// 	}
// };
export const UpdateSystemPrompt = async (systemPromptData: SystemPromptData) => {
	try {
		// Step 1: Get SystemPrompt as string
		let promptText = systemPromptData.SystemPrompt;

		// Step 2: If it's not a string, stringify it
		if (typeof promptText !== 'string') {
			promptText = JSON.stringify(promptText);
		}

		// Step 3: Attempt to parse once if it's a JSON stringified string
		try {
			const parsed = JSON.parse(promptText);
			if (typeof parsed === 'string') {
				promptText = parsed;
			}
		} catch {
			// If it's not valid JSON, ignore and keep original
		}

		// Step 4: Minimal cleaning - only handle escaped quotes if needed
		// PRESERVE ALL ORIGINAL FORMATTING: spaces, newlines, indentation, etc.
		if (promptText.includes('\\"')) {
			promptText = promptText.replace(/\\"/g, '"');     // Unescape escaped quotes only
		}

		// Step 5: Remove outer wrapping quotes if present (only if they wrap the entire string)
		if (promptText.startsWith('"') && promptText.endsWith('"') && promptText.length > 2) {
			// Check if these are actually wrapping quotes and not part of the content
			const withoutQuotes = promptText.slice(1, -1);
			// Only remove if the content doesn't contain unescaped quotes that would break JSON
			if (!withoutQuotes.includes('"') || withoutQuotes.split('"').length % 2 === 1) {
				promptText = withoutQuotes;
			}
		}

		// Step 6: Prepare final payload
		const dataToSend = {
			...systemPromptData,
			SystemPrompt: promptText,
		};

		console.log("Preserved SystemPrompt formatting:", promptText);
		console.log("Final payload:", JSON.stringify(dataToSend, null, 2));

		// Step 7: Send to API
		const response = await userApiService.post('/Assistant/UpdateSystemPrompt', dataToSend, {
			headers: {
				'Content-Type': 'application/json'
			}
		});

		console.log("API response:", response.data);

		return {
			success: true,
			data: response.data,
			message: "System prompt updated successfully"
		};
	} catch (error) {
		console.error("Error updating system prompt:", error);
		return {
			success: false,
			error: error,
			message: "Failed to update system prompt"
		};
	}
};



export const GetSystemPromptHistory = async (
	setModels: any,
	setLoading: any,
	OrganizationIds: any,
	Id: any,
    AccountIds: any,
	skip?: any,
	top?: any,
	setTotalCount?: any,
	orderByField?: any,
    filters?: any,
) => {
	try {
		setLoading(true);
		// Build the query string with pagination parameters if provided
		let queryString = `/Assistant/GetSystemPromptHistory?id=${Id}`;
		if (skip !== undefined && top !== undefined) {
			queryString += `&skip=${skip}&top=${top}`;
		}
		if (OrganizationIds) {
			queryString += `&organizationId=${OrganizationIds}`;
		}
		if (AccountIds) {
			queryString += `&accountId=${AccountIds}`;
		}

		const response = await userApiService.get(queryString);
		let apiData = response.data;

		// Pass the data to the callback function
		setModels(apiData);

	} catch (error) {
        console.error("Error fetching system prompt history:", error);
        throw error;
	} finally {
		// setLoading is handled by the callback function
	}
};
