export interface OrganizationCreateCommand {
	Name: string;
	DefaultLocation: string;
	TimeZone: string;
	DateFormat: string;
	Logo: string;
	Domain: string;
	EnableRegisterUsers: boolean;
	EnableAppData: boolean;
	EnableCustomMenu: boolean;
	TaskCompletionRedirection: string;
	ElementControlStyle: string;
	Type: string;
	Rtl: boolean;
	IsCaptchaEnabled: boolean;
	TimeFormat: string;
	ShowTerms: boolean;
	EnableNumberFormat: boolean;
	ThemeId: string;
	CanUserChangeTheme: boolean;
	ThemeType: string;
	EnableMobile: boolean;
	IsChatbotEnabled: boolean;
	DurationFormat: string;
	ReadOnlyFormat: string;
	IsCommentsEnabled: boolean;
	IsShowHideOtherEnabled: boolean;
	IsCustomUserListEnabled: boolean;
	SandboxType: string;
	DashboardViewType: string;
	CanUserChangeDashboadView: boolean;
	DropDownBehaviour: string;
	ToastNotificationEnabled: boolean;
}