function GuideTable() {
	const guides = [
		{
			name: "Sample 1",
			id: "0",
		},
		{
			name: "Sample 2",
			id: "1",
		},
		{
			name: "Sample 3",
			id: "2",
		},
		{
			name: "Sample 4",
			id: "3",
		},
		{
			name: "Sample 5",
			id: "4",
		},
	]

	return (
		<>
			{guides.map(function (data) {
				return (
					<div>
						<div
							data-testid="material-table-row-0"
							data-selected="false"
							className="guide-table"
						>
							<div className="guide-block">
								<div className="checkbox-block">
									<label
										data-testid="checkbox-container-test-id"
										className="check-label"
									>
										<div>
											<input type="checkbox" />
											<div></div>
										</div>
										<div>
											<span></span>
										</div>
									</label>
									<div>
										<span className="updated-date">{data.name}</span>
										<div className="date-text">Last Updated: Jun 04 2024, 02:31 PM</div>
										<div className="inactive-block">
											<div
												color="lightGrey"
												className="grey-bg"
											>
												<svg
													stroke="currentColor"
													fill="currentColor"
													stroke-width="0"
													viewBox="0 0 24 24"
													color="#74819B"
													height="14"
													width="14"
													xmlns="http://www.w3.org/2000/svg"
													style={{ color: "rgb(116, 129, 155)" }}
												>
													<path d="M13 10H20L11 23V14H4L13 1V10Z"></path>
												</svg>
												<span color="lightGrey">Inactive</span>
											</div>
											<div className="vert-line"></div>
											<div
												id="guide-117566-dropdown-dropdown"
												data-testid="guide-117566-dropdown"
											>
												<div className="DropdownWrapper">
													<div id="guide-117566-dropdown-toggle">
														<button
															id="edittags-button"
															data-hover="false"
															className="edittag-button"
															style={{ padding: "0px" }}
														>
															<svg
																stroke="currentColor"
																fill="currentColor"
																stroke-width="0"
																viewBox="0 0 24 24"
																height="16"
																width="16"
																xmlns="http://www.w3.org/2000/svg"
															>
																<path d="M10.9042 2.1001L20.8037 3.51431L22.2179 13.4138L13.0255 22.6062C12.635 22.9967 12.0019 22.9967 11.6113 22.6062L1.71184 12.7067C1.32131 12.3162 1.32131 11.683 1.71184 11.2925L10.9042 2.1001ZM11.6113 4.22142L3.83316 11.9996L12.3184 20.4849L20.0966 12.7067L19.036 5.28208L11.6113 4.22142ZM13.7327 10.5854C12.9516 9.80433 12.9516 8.538 13.7327 7.75695C14.5137 6.9759 15.78 6.9759 16.5611 7.75695C17.3421 8.538 17.3421 9.80433 16.5611 10.5854C15.78 11.3664 14.5137 11.3664 13.7327 10.5854Z"></path>
															</svg>
															<span>Edit Tags</span>
														</button>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div className="link-button">
									<div className="right-buttons">
										<div className="tooltip-wrapper">
											<button
												id="link-button"
												data-testid="action-link"
												className="right-icon"
											>
												<div className="svg-block">
													<svg
														stroke="currentColor"
														fill="currentColor"
														stroke-width="0"
														viewBox="0 0 24 24"
														height="20"
														width="20"
														xmlns="http://www.w3.org/2000/svg"
													>
														<path d="M14 13.5V8C14 5.79086 12.2091 4 10 4C7.79086 4 6 5.79086 6 8V13.5C6 17.0899 8.91015 20 12.5 20C16.0899 20 19 17.0899 19 13.5V4H21V13.5C21 18.1944 17.1944 22 12.5 22C7.80558 22 4 18.1944 4 13.5V8C4 4.68629 6.68629 2 10 2C13.3137 2 16 4.68629 16 8V13.5C16 15.433 14.433 17 12.5 17C10.567 17 9 15.433 9 13.5V8H11V13.5C11 14.3284 11.6716 15 12.5 15C13.3284 15 14 14.3284 14 13.5Z"></path>
													</svg>
												</div>
											</button>
										</div>
										<div style={{ position: "absolute", opacity: "0", top: "331px", left: "1146.5px" }}>
											<span>Hyperlink</span>
											<div style={{ top: "-3px", transform: "rotate(45deg)", left: "30.5px" }}></div>
										</div>
										<div className="tooltip-wrapper">
											<button
												id="clone-button"
												data-testid="action-clone"
												className="right-icon"
											>
												<div className="svg-block">
													<svg
														stroke="currentColor"
														fill="currentColor"
														stroke-width="0"
														viewBox="0 0 24 24"
														height="20"
														width="20"
														xmlns="http://www.w3.org/2000/svg"
													>
														<path d="M6.99979 7V3C6.99979 2.44772 7.4475 2 7.99979 2H20.9998C21.5521 2 21.9998 2.44772 21.9998 3V16C21.9998 16.5523 21.5521 17 20.9998 17H17V20.9925C17 21.5489 16.551 22 15.9925 22H3.00728C2.45086 22 2 21.5511 2 20.9925L2.00276 8.00748C2.00288 7.45107 2.4518 7 3.01025 7H6.99979ZM8.99979 7H15.9927C16.549 7 17 7.44892 17 8.00748V15H19.9998V4H8.99979V7ZM4.00255 9L4.00021 20H15V9H4.00255Z"></path>
													</svg>
												</div>
											</button>
										</div>
										<div style={{ position: "absolute", opacity: "0", top: "331px", left: "1187.5px" }}>
											<span>Clone</span>
											<div style={{ top: "-3px", transform: "rotate(45deg)", left: "21.5px;" }}></div>
										</div>
										<div className="tooltip-wrapper">
											<button
												id="settings-button"
												data-testid="action-settings"
												className="right-icon"
											>
												<div className="svg-block">
													<svg
														stroke="currentColor"
														fill="currentColor"
														stroke-width="0"
														viewBox="0 0 24 24"
														height="20"
														width="20"
														xmlns="http://www.w3.org/2000/svg"
													>
														<path d="M2 11.9998C2 11.1353 2.1097 10.2964 2.31595 9.49631C3.40622 9.55283 4.48848 9.01015 5.0718 7.99982C5.65467 6.99025 5.58406 5.78271 4.99121 4.86701C6.18354 3.69529 7.66832 2.82022 9.32603 2.36133C9.8222 3.33385 10.8333 3.99982 12 3.99982C13.1667 3.99982 14.1778 3.33385 14.674 2.36133C16.3317 2.82022 17.8165 3.69529 19.0088 4.86701C18.4159 5.78271 18.3453 6.99025 18.9282 7.99982C19.5115 9.01015 20.5938 9.55283 21.6841 9.49631C21.8903 10.2964 22 11.1353 22 11.9998C22 12.8643 21.8903 13.7032 21.6841 14.5033C20.5938 14.4468 19.5115 14.9895 18.9282 15.9998C18.3453 17.0094 18.4159 18.2169 19.0088 19.1326C17.8165 20.3043 16.3317 21.1794 14.674 21.6383C14.1778 20.6658 13.1667 19.9998 12 19.9998C10.8333 19.9998 9.8222 20.6658 9.32603 21.6383C7.66832 21.1794 6.18354 20.3043 4.99121 19.1326C5.58406 18.2169 5.65467 17.0094 5.0718 15.9998C4.48848 14.9895 3.40622 14.4468 2.31595 14.5033C2.1097 13.7032 2 12.8643 2 11.9998ZM6.80385 14.9998C7.43395 16.0912 7.61458 17.3459 7.36818 18.5236C7.77597 18.8138 8.21005 19.0652 8.66489 19.2741C9.56176 18.4712 10.7392 17.9998 12 17.9998C13.2608 17.9998 14.4382 18.4712 15.3351 19.2741C15.7899 19.0652 16.224 18.8138 16.6318 18.5236C16.3854 17.3459 16.566 16.0912 17.1962 14.9998C17.8262 13.9085 18.8225 13.1248 19.9655 12.7493C19.9884 12.5015 20 12.2516 20 11.9998C20 11.7481 19.9884 11.4981 19.9655 11.2504C18.8225 10.8749 17.8262 10.0912 17.1962 8.99982C16.566 7.90845 16.3854 6.65378 16.6318 5.47605C16.224 5.18588 15.7899 4.93447 15.3351 4.72552C14.4382 5.52844 13.2608 5.99982 12 5.99982C10.7392 5.99982 9.56176 5.52844 8.66489 4.72552C8.21005 4.93447 7.77597 5.18588 7.36818 5.47605C7.61458 6.65378 7.43395 7.90845 6.80385 8.99982C6.17376 10.0912 5.17754 10.8749 4.03451 11.2504C4.01157 11.4981 4 11.7481 4 11.9998C4 12.2516 4.01157 12.5015 4.03451 12.7493C5.17754 13.1248 6.17376 13.9085 6.80385 14.9998ZM12 14.9998C10.3431 14.9998 9 13.6567 9 11.9998C9 10.343 10.3431 8.99982 12 8.99982C13.6569 8.99982 15 10.343 15 11.9998C15 13.6567 13.6569 14.9998 12 14.9998ZM12 12.9998C12.5523 12.9998 13 12.5521 13 11.9998C13 11.4475 12.5523 10.9998 12 10.9998C11.4477 10.9998 11 11.4475 11 11.9998C11 12.5521 11.4477 12.9998 12 12.9998Z"></path>
													</svg>
												</div>
											</button>
										</div>
										<div style={{ position: "absolute", opacity: "0", top: "331px", left: "1213.5px" }}>
											<span>Settings</span>
											<div style={{ top: "-3px", transform: "rotate(45deg)", left: "27.5px" }}></div>
										</div>
										<div className="tooltip-wrapper">
											<button
												id="delete-button"
												className="del-icon"
											>
												<div className="svg-block">
													<svg
														stroke="currentColor"
														fill="currentColor"
														stroke-width="0"
														viewBox="0 0 24 24"
														height="20"
														width="20"
														xmlns="http://www.w3.org/2000/svg"
													>
														<path d="M7 4V2H17V4H22V6H20V21C20 21.5523 19.5523 22 19 22H5C4.44772 22 4 21.5523 4 21V6H2V4H7ZM6 6V20H18V6H6ZM9 9H11V17H9V9ZM13 9H15V17H13V9Z"></path>
													</svg>
												</div>
											</button>
										</div>
										<div style={{ position: "absolute", opacity: "0", top: "331px", left: "1250px" }}>
											<span>Delete</span>
											<div style={{ top: "-3px", transform: "rotate(45deg)", left: "23px" }}></div>
										</div>
									</div>
									<div className="default-text">Default</div>
								</div>
							</div>
						</div>
					</div>
				)
			})}
		</>
	)
}

export default GuideTable
