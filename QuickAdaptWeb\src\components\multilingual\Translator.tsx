import axios from "axios";

import { useState } from "react";
const API_URL = "https://translate.googleapis.com/translate_a/single";
 
export const translateText = async (text:string, sourceLanguage:string, targetLanguage:string) => {
  try {
    const response = await axios.get(API_URL, {
      params: {
        client: 'gtx',
        sl: sourceLanguage,
        tl: targetLanguage,
        dt: 't',
        q: text
      }
    });
    return response.data[0][0][0];
  } catch (error) {
   
  }
};

