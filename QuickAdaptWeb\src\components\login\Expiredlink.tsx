import React from 'react';
import linkexpirationimage from "../../assets/icons/linkexpirationimage.png";
import expirelinksvgimage from "../../assets/icons/Expiredlink.svg"
import { useNavigate } from 'react-router-dom';
import { Button } from '@mui/material';
const ExpiredLink = () => {  
    const navigate = useNavigate();
    const onHandleClick = () => {
        navigate("/login");
    };

    return (
        <div style={{ textAlign: 'center', backgroundColor: '#f0f4f8', padding: '60px',marginTop:"-40px" }}>
            <h1>Oops!</h1>
            <img src={expirelinksvgimage} alt="Reset Password Illustration" />
            <p>Reset Password Link Expired</p>

            <Button  href="https://app.quickadopt.in/Login" variant='contained' style={{ marginTop: '20px', padding: '10px 20px', fontSize: '16px',textTransform:"none" }}>Back To Login Page</Button>
            {/* <p>
                <a href="#contact">If this continues to persist, contact us.</a>
            </p> */}
        </div>);
}; export default ExpiredLink;