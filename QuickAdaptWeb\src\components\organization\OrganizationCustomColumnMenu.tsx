import React, { useState, useEffect } from "react";
import { GridColumnMenu, GridColumnMenuHideItem, GridColumnMenuProps } from "@mui/x-data-grid";
import { fetchOrganizations } from "../../services/OrganizationService";
import OrganizationCustomColumnMenuItem from "./OrganizationcustomcolumnMenuItem";
import { ModelTrainingSharp } from "@mui/icons-material";
import { Organization } from "../../models/Organization";
interface OrganizationCustomColumnMenuProps extends GridColumnMenuProps {
	column: string;
	skip: number;
	top: number;
	OrganizationId: any;
	setTotalcount: (count: number) => void;
	orderByFields: string;
	setModels: (models: any[]) => void;
	sortModel: any;
	setLoading: (loading: boolean) => void;
	filters: any;
	setFilters: (filters: any) => void;
	options: string[];
	onSearch: (value: string[]) => void;
	hideMenu: (event: React.SyntheticEvent<Element, Event>) => void;
	models: any;
	modelsData: any;
	optionsModel: any;
	paginationModel: any;

}
 
const OrganizationCustomColumnMenu: React.FC<OrganizationCustomColumnMenuProps> = (props) => {
	const {
	  column,
	  skip,
	  top,
	  OrganizationId,
	  setTotalcount,
	  orderByFields,
	  setModels,
	  sortModel,
	  setLoading,
	  filters,
	  setFilters,
	  options,
		onSearch,
		hideMenu,
		models,
		modelsData,
		optionsModel,
		paginationModel,
	  ...other
	} = props;
  
	return (
		<div>
	  <GridColumnMenu
		{...other}
		slots={{
		  columnMenuUserItem: (menuProps) => (
			<OrganizationCustomColumnMenuItem
			  column={column}
			  setModels={setModels}
			  setLoading={setLoading}
			  skip={skip}
			  top={top}
			  OrganizationId={OrganizationId}
			  sortModel={sortModel}
			  setTotalcount={setTotalcount}
			  orderByFields={orderByFields}
			  filters={filters}
			  setFilters={setFilters}
			options={options}
					onSearch={onSearch}
					models={models}
					modelsData={modelsData}
			hideMenu={(event) => hideMenu(event)}
					open={menuProps.open}           // Pass the open prop
					colDef={menuProps.colDef} 
					optionsModel={optionsModel}
					paginationModel ={paginationModel}
			/>
		  ),
		  GridColumnMenuHideItem: null,
		}}
		slotProps={{
		  columnMenuUserItem: {
			displayOrder: 15,
		  },
			}}
			
			hideMenu={hideMenu}
		/>
	

			</div>
	
	);
  };
  
  export default OrganizationCustomColumnMenu;