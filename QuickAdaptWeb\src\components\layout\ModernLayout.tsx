import React from 'react';
import { Box } from '@mui/material';
import { styled } from '@mui/material/styles';

interface ModernLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  showSidebar?: boolean;
}

const LayoutContainer = styled(Box)({
  display: 'flex',
  minHeight: '100vh',
  backgroundColor: 'var(--color-gray-50)',
});

const SidebarContainer = styled(Box)<{ show: boolean }>(({ show }) => ({
  width: show ? '280px' : '0px',
  transition: 'var(--transition-normal)',
  overflow: 'hidden',
  backgroundColor: 'var(--color-white)',
  borderRight: '1px solid var(--color-gray-200)',
  boxShadow: 'var(--shadow-sm)',
  position: 'relative',
  zIndex: 10,
}));

const MainContent = styled(Box)<{ sidebarOpen: boolean }>(({ sidebarOpen }) => ({
  flex: 1,
  marginLeft: sidebarOpen ? '0' : '0',
  transition: 'var(--transition-normal)',
  overflow: 'hidden',
  position: 'relative',
}));

const ContentArea = styled(Box)({
  padding: 0,
  height: '100%',
  overflow: 'auto',
});

const ModernLayout: React.FC<ModernLayoutProps> = ({
  children,
  sidebar,
  showSidebar = true,
}) => {
  return (
    <LayoutContainer>
      {sidebar && (
        <SidebarContainer show={showSidebar}>
          {sidebar}
        </SidebarContainer>
      )}
      
      <MainContent sidebarOpen={showSidebar}>
        <ContentArea>
          {children}
        </ContentArea>
      </MainContent>
    </LayoutContainer>
  );
};

export default ModernLayout;
