import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
    Box,
    Button,
    Paper,
    IconButton,
    Tooltip,
    Switch,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import EmailIcon from '@mui/icons-material/Email';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { isSidebarOpen, subscribe } from "../adminMenu/sidemenustate";
import { useSnackbar } from '../../SnackbarContext';

const FileUploads = () => {
    const [images, setImages] = useState([]);
    const [uploadResults, setUploadResults] = useState([]);
    const [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());
    const { openSnackbar } = useSnackbar();

    const handleImageChange = (e:any) => {
        const newImages = Array.from(e.target.files).map((file:any, index) => ({
            id: images.length + index + 1,
            name: file.name,
            link: URL.createObjectURL(file),
            file,
        }));
        //setImages((prevImages) => [...prevImages, ...newImages]);
    };

    useEffect(() => {
        const unsubscribe = subscribe(setSidebarOpen);
        return () => unsubscribe();
    }, []);

    const handleUpload = async () => {
        const formData = new FormData();
        images.forEach((image:any) => {
            formData.append('files', image.file);
        });

        try {
            const response = await axios.post('/api/ImageUpload/upload', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });
            setUploadResults(response.data);
        } catch (error) {
           openSnackbar("Failed to upload image.", "error");

        }
    };

    const handleEdit = (id:any) => {
    };

    const handleReplace = (id:any) => {
    };

    const handleDelete = (id:any) => {
        setImages(images.filter((image:any) => image.id !== id));
    };

    const columns: GridColDef[] = [
        { field: 'id', headerName: 'S.no', width: sidebarOpen ? 50 : 100 },
        { field: 'name', headerName: 'Name', width: sidebarOpen ? 103 : 150 },
        {
            field: 'link',
            headerName: 'Link',
            width: sidebarOpen ? 103 : 150,
            renderCell: (params) => (
                <a href={params.value} target="_blank" rel="noopener noreferrer">
                    View
                </a>
            ),
        },
        {
            field: 'actions',
            headerName: 'Actions',
            sortable: false,
            width: 300,
            renderCell: (params) => {
                const id = params.row.id;
                return (
                    <Box display="flex" justifyContent="space-between" width="100%">
                        <Tooltip title="Send Email">
                            <IconButton>
                                <EmailIcon />
                            </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit">
                            <IconButton onClick={() => handleEdit(id)}>
                                <EditIcon />
                            </IconButton>
                        </Tooltip>
                        <Tooltip title="Replace">
                            <IconButton onClick={() => handleReplace(id)}>
                                <CloudUploadIcon />
                            </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete">
                            <IconButton onClick={() => handleDelete(id)}>
                                <DeleteIcon />
                            </IconButton>
                        </Tooltip>
                        <Tooltip title="Toggle">
                            <Switch defaultChecked />
                        </Tooltip>
                    </Box>
                );
            },
        },
    ];

    return (
        <div style={{ marginLeft: sidebarOpen ? "160px" : "" }}>
            <Box m={4}>
                <Box display="flex" justifyContent="flex-end" mb={2}>
                    <Button
                        variant="contained"
                        color="primary"
                        startIcon={<AddIcon />}
                        component="label"
                    >
                        + Add File
                        <input type="file" multiple hidden onChange={handleImageChange} />
                    </Button>
                </Box>
                <Paper>
                    <DataGrid
                        rows={images}
                        columns={columns}
                       // pageSize=5
                        autoHeight
                        //disableSelectionOnClick
                    />
                </Paper>
            </Box>
        </div>
    );
};

export default FileUploads;
