import React, { useState } from 'react';
import { Contain<PERSON>, <PERSON>, Grid, <PERSON>, <PERSON>po<PERSON>, Avatar, InputBase, Badge, IconButton, Tooltip, Chip, Tabs, Tab } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import NotificationsIcon from '@mui/icons-material/Notifications';
import PersonIcon from '@mui/icons-material/Person';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import GroupIcon from '@mui/icons-material/Group';
import StarIcon from '@mui/icons-material/Star';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import BoltIcon from '@mui/icons-material/Bolt';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';
import './AnalyticsDashboard.scss';

const activeUsersData = [
  { date: 'Jan 1', users: 8000 },
  { date: 'Jan 8', users: 9000 },
  { date: 'Jan 15', users: 10000 },
  { date: 'Jan 22', users: 11000 },
  { date: 'Jan 29', users: 11500 },
  { date: 'Feb 5', users: 12000 },
  { date: 'Feb 12', users: 13000 },
  { date: 'Feb 19', users: 14000 },
];
const newUsersData = [
  { date: 'Jan 1', users: 450 },
  { date: 'Jan 8', users: 400 },
  { date: 'Jan 15', users: 650 },
  { date: 'Jan 22', users: 600 },
  { date: 'Jan 29', users: 488 },
  { date: 'Feb 5', users: 700 },
  { date: 'Feb 12', users: 600 },
  { date: 'Feb 19', users: 500 },
];
const guides = [
  { name: 'Product Onboarding', views: 1400, completed: 1247, drop: 11, rate: 89, color: '#a385ff' },
  { name: 'Feature Discovery', views: 1174, completed: 892, drop: 24, rate: 76, color: '#6ec6ff' },
  { name: 'Advanced Settings', views: 932, completed: 634, drop: 32, rate: 68, color: '#ffd54f', warn: true },
  { name: 'Integration Setup', views: 543, completed: 445, drop: 18, rate: 82, color: '#82e6c7' },
  { name: 'Reporting Tutorial', views: 430, completed: 305, drop: 29, rate: 71, color: '#ff8a80' },
];
const funnel = [
  { step: 'Guide Started', users: 1000, retention: 100, avg: '0s', drop: 0, color: '#e8f5e9' },
  { step: 'Step 1: Welcome', users: 850, retention: 85, avg: '45s', drop: 15, color: '#e3f2fd' },
  { step: 'Step 2: Setup', users: 720, retention: 72, avg: '2m 15s', drop: 15.3, color: '#fffde7' },
  { step: 'Step 3: Configuration', users: 650, retention: 65, avg: '1m 30s', drop: 9.7, color: '#ede7f6' },
  { step: 'Guide Completed', users: 580, retention: 58, avg: '3m 45s', drop: 7, color: '#ffebee' },
];

const kpis = [
  {
    label: 'Active Users',
    value: '12,847',
    change: '+12.5%',
    sub: 'Monthly active users',
    icon: <GroupIcon sx={{ color: '#2196f3' }} />, color: '#e3f2fd',
  },
  {
    label: 'Guide Completion',
    value: '87.3%',
    change: '+5.2%',
    sub: 'Average completion rate',
    icon: <CheckCircleIcon sx={{ color: '#43a047' }} />, color: '#e8f5e9',
  },
  {
    label: 'Agent Actions',
    value: '45,231',
    change: '+18.7%',
    sub: 'AI agent interactions',
    icon: <BoltIcon sx={{ color: '#8e24aa' }} />, color: '#f3e5f5',
  },
  {
    label: 'User Satisfaction',
    value: '4.6/5',
    change: '+0.3',
    sub: 'Average NPS score',
    icon: <StarIcon sx={{ color: '#ffb300' }} />, color: '#fff8e1',
  },
  {
    label: 'Hours Saved',
    value: '2,847',
    change: '+23.1%',
    sub: 'Automation impact',
    icon: <AccessTimeIcon sx={{ color: '#e53935' }} />, color: '#ffebee',
  },
];

const topAgents = [
  { name: 'Sarah Choi', score: 92, tickets: 1247, rate: 98, color: '#a385ff' },
  { name: 'Mika Rodriguez', score: 89, tickets: 1140, rate: 95, color: '#6ec6ff' },
  { name: 'Emma Thompson', score: 85, tickets: 1020, rate: 91, color: '#ffd54f' },
  { name: 'Daniel Kim', score: 82, tickets: 987, rate: 89, color: '#82e6c7' },
];
const agentActivity = [
  { status: 'Tickets Resolved', count: 2450, percent: 100, color: '#e8f5e9' },
  { status: 'Free/Available', count: 2325, percent: 95, color: '#e3f2fd' },
  { status: 'In Progress', count: 2140, percent: 87, color: '#fffde7' },
  { status: 'Reached', count: 1945, percent: 79, color: '#ede7f6' },
];
const responseTimeData = [
  { date: 'Week 1', time: 4.2 },
  { date: 'Week 2', time: 3.8 },
  { date: 'Week 3', time: 3.5 },
  { date: 'Week 4', time: 3.7 },
];
const resolutionRateData = [
  { date: 'Week 1', rate: 78 },
  { date: 'Week 2', rate: 82 },
  { date: 'Week 3', rate: 85 },
  { date: 'Week 4', rate: 88 },
];

const AnalyticsDashboard = () => {
  const [isHovered, setIsHovered] = useState(false);
  const [selectedTab, setSelectedTab] = useState(0);

  const tabLabels = [
    'Overview & Feedback',
    'Agents',
    'Analytics Active Users',
  ];
  const dummyAgents = [
    { name: 'Alex Johnson', status: 'Active', actions: 120 },
    { name: 'Maria Garcia', status: 'Inactive', actions: 85 },
    { name: 'David Chen', status: 'Active', actions: 102 },
    { name: 'Sarah Wilson', status: 'Active', actions: 97 },
  ];

  return (
    <div className='qadpt-web'>
        <div className='qadpt-webcontent'>
    <div className='qadpt-web'>
        <div className='qadpt-webcontent'>
    <Container maxWidth="xl" sx={{ background: '#f6f9ff', minHeight: '100vh', py: 3 }}>
      {/* Header */}
      <Box display="flex" alignItems="center" justifyContent="space-between" mb={4}>
        <Box display="flex" alignItems="center" gap={2}>
          <Avatar sx={{ bgcolor: '#7c4dff', width: 40, height: 40 }}>QA</Avatar>
          <Typography variant="h5" fontWeight={700} color="#2d2357">Quick Adopt</Typography>
          <Box ml={2} px={1} py={0.5} bgcolor="#e3e3fa" borderRadius={2} fontSize={12} color="#5e35b1">Analytics</Box>
        </Box>
        <Box display="flex" alignItems="center" gap={2}>
          <Box sx={{ bgcolor: '#ede7f6', px: 2, py: 0.5, borderRadius: 2, display: 'flex', alignItems: 'center' }}>
            <SearchIcon sx={{ color: '#7c4dff', mr: 1 }} />
            <InputBase placeholder="Search dashboard..." sx={{ fontSize: 14, width: 180 }} />
          </Box>
          <IconButton>
            <Badge badgeContent={3} color="error">
              <NotificationsIcon />
            </Badge>
          </IconButton>
          <Avatar sx={{ bgcolor: '#ede7f6', color: '#7c4dff', width: 36, height: 36 }}><PersonIcon /></Avatar>
        </Box>
      </Box>

      {/* Tabs Navigation */}
      <Tabs
        value={selectedTab}
        onChange={(e, newValue) => setSelectedTab(newValue)}
        indicatorColor="primary"
        textColor="primary"
        variant="fullWidth"
        sx={{ mb: 3, width: '100%', background: '#fff', borderRadius: 2, boxShadow: 1 }}
      >
        {tabLabels.map((label, idx) => (
          <Tab key={label} label={label} />
        ))}
      </Tabs>

      {/* Tab content rendering */}
      {selectedTab === 0 && (
        <React.Fragment>
          {/* Overview & Feedback content here */}
          {/* KPI Section */}
          <Typography variant="h6" fontWeight={700} color="#2d2357" mb={0.5}>Key Performance Indicators</Typography>
          <Typography variant="body2" color="#7b809a" mb={2}>
            Real-time insights into your platform performance
          </Typography>
          <Grid container spacing={3} mb={4}>
            {kpis.map((kpi, idx) => (
              <Grid item xs={12} sm={6} md={2.4} key={kpi.label}>
                <Card sx={{ p: 2.5, borderRadius: 3, boxShadow: 2, bgcolor: kpi.color, minHeight: 120 }}>
                  <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography variant="subtitle2" color="#7b809a">{kpi.label}</Typography>
                      <Typography variant="h5" fontWeight={700} color="#2d2357">{kpi.value}</Typography>
                      <Box display="flex" alignItems="center" gap={1} mt={0.5}>
                        <Typography variant="caption" color="#43a047" fontWeight={700}>{kpi.change}</Typography>
                        <Typography variant="caption" color="#7b809a">{kpi.sub}</Typography>
                      </Box>
                    </Box>
                    <Box>{kpi.icon}</Box>
                  </Box>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* User Adoption & Engagement */}
          <Typography variant="h6" fontWeight={700} color="#2962ff" mb={0.5}>User Adoption & Engagement</Typography>
          <Typography variant="body2" color="#7b809a" mb={2}>
            Track user growth and engagement patterns
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={7}>
              <Card
                className={isHovered ? 'card-highlight' : ''}
                sx={{ p: 3, borderRadius: 3, minHeight: 320, boxShadow: 1 }}
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
              >
                <Box display="flex" alignItems="center" gap={1} mb={2}>
                  <GroupIcon sx={{ color: '#2196f3' }} />
                  <Typography fontWeight={600}>Active Users Trend</Typography>
                </Box>
                <Typography variant="caption" color="#7b809a">Daily active users over time</Typography>
                <div className="chart-container">
                  <Box height={220}>
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={activeUsersData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <RechartsTooltip formatter={(value:any) => [`${value} users`, 'Active Users']} />
                        <Line type="monotone" dataKey="users" stroke="#7c4dff" strokeWidth={3} dot={{ r: 5 }} />
                      </LineChart>
                    </ResponsiveContainer>
                  </Box>
                </div>
              </Card>
            </Grid>
            <Grid item xs={12} md={5}>
              <Card
                className={isHovered ? 'card-highlight' : ''}
                sx={{ p: 3, borderRadius: 3, minHeight: 320, boxShadow: 1, bgcolor: '#e8f5e9' }}
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
              >
                <Box display="flex" alignItems="center" gap={1} mb={2}>
                  <CheckCircleIcon sx={{ color: '#43a047' }} />
                  <Typography fontWeight={600}>New Users Onboarded</Typography>
                </Box>
                <Typography variant="caption" color="#7b809a">Weekly new user registrations</Typography>
                <div className="chart-container">
                  <Box height={220}>
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={newUsersData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                        <defs>
                          <linearGradient id="colorUsers" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#43a047" stopOpacity={0.3}/>
                            <stop offset="95%" stopColor="#43a047" stopOpacity={0}/>
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <RechartsTooltip formatter={(value:any) => [`${value} users`, 'New Users']} />
                        <Area type="monotone" dataKey="users" stroke="#43a047" fillOpacity={1} fill="url(#colorUsers)" />
                      </AreaChart>
                    </ResponsiveContainer>
                  </Box>
                </div>
              </Card>
            </Grid>
          </Grid>

          {/* Guide & Content Analytics */}
          <Typography variant="h6" fontWeight={700} color="#7c4dff" mt={5} mb={0.5}>Guide & Content Analytics</Typography>
          <Typography variant="body2" color="#7b809a" mb={2}>
            Analyze guide performance and user journey
          </Typography>
          <Grid container spacing={3}>
            {/* Top Guides Performance */}
            <Grid item xs={12} md={6}>
              <Card sx={{ p: 3, borderRadius: 3, minHeight: 420, boxShadow: 1, bgcolor: '#fce4ec' }}>
                <Box display="flex" alignItems="center" gap={1} mb={2}>
                  <BoltIcon sx={{ color: '#d81b60' }} />
                  <Typography fontWeight={600}>Top Guides Performance</Typography>
                </Box>
                {guides.map((g, i) => (
                  <Box key={g.name} mb={2}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Chip label={`${g.rate}%`} size="small" sx={{ bgcolor: g.color, color: '#fff', fontWeight: 700 }} />
                      <Typography fontWeight={500}>{g.name}</Typography>
                      {g.warn && <WarningAmberIcon sx={{ color: '#ff9800', fontSize: 18, ml: 1 }} />}
                    </Box>
                    <Typography variant="caption" color="#7b809a">Completion Rate</Typography>
                    <Tooltip title={<>
                      <div>Views: <b>{g.views}</b></div>
                      <div>Completed: <b>{g.completed}</b></div>
                      <div>Drop-off: <b>{g.drop}%</b></div>
                    </>} arrow>
                      <Box mt={0.5} mb={0.5} bgcolor="#eee" borderRadius={2} height={10} position="relative" sx={{ cursor: 'pointer' }}>
                        <Box bgcolor={g.color} borderRadius={2} height={10} width={`${g.rate}%`} position="absolute" top={0} left={0} />
                      </Box>
                    </Tooltip>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Box display="flex" alignItems="center" gap={1}>
                        <Chip label={`${g.views} Views`} size="small" sx={{ bgcolor: '#ede7f6', color: '#7c4dff', fontWeight: 700 }} />
                        <Chip label={`${g.completed} Completed`} size="small" sx={{ bgcolor: '#e8f5e9', color: '#43a047', fontWeight: 700 }} />
                      </Box>
                      <Chip label={`${g.drop}% Drop-off`} size="small" sx={{ bgcolor: '#ffebee', color: '#e53935', fontWeight: 700 }} />
                    </Box>
                  </Box>
                ))}
              </Card>
            </Grid>
            {/* Interactive Guide Funnel */}
            <Grid item xs={12} md={6}>
              <Card sx={{ p: 3, borderRadius: 3, minHeight: 420, boxShadow: 1, bgcolor: '#fffde7' }}>
                <Box display="flex" alignItems="center" gap={1} mb={2}>
                  <CheckCircleIcon sx={{ color: '#ffb300' }} />
                  <Typography fontWeight={600}>Interactive Guide Funnel</Typography>
                </Box>
                <Typography variant="body2" color="#7b809a" mb={2}>
                  Display guide user journey with conversion and drop-off analysis
                </Typography>
                {funnel.map((f, i) => (
                  <Tooltip key={f.step} title={<>
                    <div>Users: <b>{f.users}</b></div>
                    <div>Retention: <b>{f.retention}%</b></div>
                    <div>Avg time: <b>{f.avg}</b></div>
                    <div>Drop-off: <b>{f.drop}%</b></div>
                  </>} arrow>
                    <Box mb={2} p={2} bgcolor={f.color} borderRadius={2} boxShadow={0} sx={{ cursor: 'pointer' }}>
                      <Box display="flex" justifyContent="space-between" alignItems="center">
                        <Typography fontWeight={500}>{f.step}</Typography>
                        <Typography variant="h6" color="#7c4dff">{f.users.toLocaleString()}</Typography>
                      </Box>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mt={1}>
                        <Typography variant="caption" color="#7b809a">Retention: {f.retention}%</Typography>
                        <Typography variant="caption" color="#7b809a">Avg time: {f.avg}</Typography>
                        <Typography variant="caption" color="#e53935">Drop-off: {f.drop}%</Typography>
                      </Box>
                    </Box>
                  </Tooltip>
                ))}
                <Box display="flex" justifyContent="space-between" alignItems="center" mt={3}>
                  <Box textAlign="center">
                    <Typography fontWeight={700} color="#43a047">58%</Typography>
                    <Typography variant="caption" color="#7b809a">Overall Completion</Typography>
                  </Box>
                  <Box textAlign="center">
                    <Typography fontWeight={700} color="#e53935">42%</Typography>
                    <Typography variant="caption" color="#7b809a">Overall Drop-off</Typography>
                  </Box>
                </Box>
              </Card>
            </Grid>
          </Grid>
        </React.Fragment>
      )}
      {selectedTab === 1 && (
        <React.Fragment>
          <Grid container spacing={3} mb={2}>
            {/* Left: Top Agent Performance */}
            <Grid item xs={12} md={6}>
              <Card sx={{ p: 3, borderRadius: 3, boxShadow: 1 }}>
                <Typography fontWeight={700} color="#7c4dff" mb={1}>Top Agents Performance</Typography>
                <Typography variant="body2" color="#7b809a" mb={2}>Comprehensive agent analytics with completion and engagement metrics</Typography>
                {topAgents.map(agent => (
                  <Box key={agent.name} mb={2} bgcolor={agent.color + '22'} borderRadius={2} p={2}>
                    <Box display="flex" alignItems="center" gap={2} mb={1}>
                      <Avatar sx={{ bgcolor: agent.color }}>{agent.name[0]}</Avatar>
                      <Typography fontWeight={600}>{agent.name}</Typography>
                      <Chip label={`${agent.score}%`} size="small" sx={{ bgcolor: agent.color, color: '#fff', fontWeight: 700 }} />
                    </Box>
                    <Typography variant="caption" color="#7b809a">Completion Rate</Typography>
                    <Box mt={0.5} mb={0.5} bgcolor="#eee" borderRadius={2} height={10} position="relative" sx={{ cursor: 'pointer' }}>
                      <Box bgcolor={agent.color} borderRadius={2} height={10} width={`${agent.score}%`} position="absolute" top={0} left={0} />
                    </Box>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Chip label={`${agent.tickets} Tickets`} size="small" sx={{ bgcolor: '#ede7f6', color: '#7c4dff', fontWeight: 700 }} />
                      <Chip label={`${agent.rate}% Resolution`} size="small" sx={{ bgcolor: '#e8f5e9', color: '#43a047', fontWeight: 700 }} />
                      <Chip label={`${100-agent.score}% Drop-off`} size="small" sx={{ bgcolor: '#ffebee', color: '#e53935', fontWeight: 700 }} />
                    </Box>
                  </Box>
                ))}
              </Card>
            </Grid>
            {/* Right: Agent Activity Flow */}
            <Grid item xs={12} md={6}>
              <Card sx={{ p: 3, borderRadius: 3, boxShadow: 1, bgcolor: '#fffde7' }}>
                <Typography fontWeight={700} color="#ff9800" mb={1}>Agent Activity Flow</Typography>
                <Typography variant="body2" color="#7b809a" mb={2}>Step-by-step agent journey with conversion and drop-off analysis</Typography>
                {agentActivity.map((act, i) => (
                  <Box key={act.status} mb={2} p={2} bgcolor={act.color} borderRadius={2} boxShadow={0}>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography fontWeight={500}>{act.status}</Typography>
                      <Typography variant="h6" color="#7c4dff">{act.count.toLocaleString()}</Typography>
                    </Box>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mt={1}>
                      <Typography variant="caption" color="#43a047">{act.percent}% Retention</Typography>
                      <Typography variant="caption" color="#e53935">{100-act.percent}% Drop-off</Typography>
                    </Box>
                  </Box>
                ))}
                <Box display="flex" justifyContent="space-between" alignItems="center" mt={3}>
                  <Box textAlign="center">
                    <Typography fontWeight={700} color="#43a047">88%</Typography>
                    <Typography variant="caption" color="#7b809a">Overall Completion</Typography>
                  </Box>
                  <Box textAlign="center">
                    <Typography fontWeight={700} color="#e53935">12%</Typography>
                    <Typography variant="caption" color="#7b809a">Total Drop-off</Typography>
                  </Box>
                </Box>
              </Card>
            </Grid>
          </Grid>
          {/* Charts row */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card sx={{ p: 3, borderRadius: 3, minHeight: 220, boxShadow: 1 }}>
                <Typography fontWeight={600} mb={2}>Agent Response Time</Typography>
                <ResponsiveContainer width="100%" height={120}>
                  <LineChart data={responseTimeData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <RechartsTooltip formatter={(value:any) => [`${value} hrs`, 'Response Time']} />
                    <Line type="monotone" dataKey="time" stroke="#7c4dff" strokeWidth={3} dot={{ r: 5 }} />
                  </LineChart>
                </ResponsiveContainer>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card sx={{ p: 3, borderRadius: 3, minHeight: 220, boxShadow: 1 }}>
                <Typography fontWeight={600} mb={2}>Resolution Rate Trends</Typography>
                <ResponsiveContainer width="100%" height={120}>
                  <AreaChart data={resolutionRateData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <RechartsTooltip formatter={(value:any) => [`${value}%`, 'Resolution Rate']} />
                    <Area type="monotone" dataKey="rate" stroke="#43a047" fillOpacity={0.5} fill="#43a047" />
                  </AreaChart>
                </ResponsiveContainer>
              </Card>
            </Grid>
          </Grid>
        </React.Fragment>
      )}
      {selectedTab === 2 && (
        <React.Fragment>
          {/* Analytics Active Users content here */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={7}>
              <Card
                className={isHovered ? 'card-highlight' : ''}
                sx={{ p: 3, borderRadius: 3, minHeight: 320, boxShadow: 1 }}
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
              >
                <Box display="flex" alignItems="center" gap={1} mb={2}>
                  <GroupIcon sx={{ color: '#2196f3' }} />
                  <Typography fontWeight={600}>Active Users Trend</Typography>
                </Box>
                <Typography variant="caption" color="#7b809a">Daily active users over time</Typography>
                <div className="chart-container">
                  <Box height={220}>
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={activeUsersData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <RechartsTooltip formatter={(value:any) => [`${value} users`, 'Active Users']} />
                        <Line type="monotone" dataKey="users" stroke="#7c4dff" strokeWidth={3} dot={{ r: 5 }} />
                      </LineChart>
                    </ResponsiveContainer>
                  </Box>
                </div>
              </Card>
            </Grid>
            <Grid item xs={12} md={5}>
              <Card
                className={isHovered ? 'card-highlight' : ''}
                sx={{ p: 3, borderRadius: 3, minHeight: 320, boxShadow: 1, bgcolor: '#e8f5e9' }}
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
              >
                <Box display="flex" alignItems="center" gap={1} mb={2}>
                  <CheckCircleIcon sx={{ color: '#43a047' }} />
                  <Typography fontWeight={600}>New Users Onboarded</Typography>
                </Box>
                <Typography variant="caption" color="#7b809a">Weekly new user registrations</Typography>
                <div className="chart-container">
                  <Box height={220}>
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={newUsersData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                        <defs>
                          <linearGradient id="colorUsers" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#43a047" stopOpacity={0.3}/>
                            <stop offset="95%" stopColor="#43a047" stopOpacity={0}/>
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <RechartsTooltip formatter={(value:any) => [`${value} users`, 'New Users']} />
                        <Area type="monotone" dataKey="users" stroke="#43a047" fillOpacity={1} fill="url(#colorUsers)" />
                      </AreaChart>
                    </ResponsiveContainer>
                  </Box>
                </div>
              </Card>
            </Grid>
          </Grid>
        </React.Fragment>
      )}
    </Container>
    </div>
      </div>
       </div>
    </div>
  );
};

export default AnalyticsDashboard;