import React, { useState } from 'react';
import { Container, <PERSON>, Grid, Card, Typo<PERSON>, Avatar, InputBase, Badge, IconButton, <PERSON><PERSON>, Tab } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import NotificationsIcon from '@mui/icons-material/Notifications';
import PersonIcon from '@mui/icons-material/Person';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import GroupIcon from '@mui/icons-material/Group';
import StarIcon from '@mui/icons-material/Star';
import BoltIcon from '@mui/icons-material/Bolt';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, Pie, Cell, Bar<PERSON>hart, Bar } from 'recharts';
import './AnalyticsDashboard.scss';

// Data for User Activity Trends chart
const userActivityData = [
  { month: 'Jan', active: 8000, new: 450, returning: 7550 },
  { month: 'Feb', active: 9000, new: 400, returning: 8600 },
  { month: 'Mar', active: 10000, new: 650, returning: 9350 },
  { month: 'Apr', active: 11000, new: 600, returning: 10400 },
  { month: 'May', active: 11500, new: 488, returning: 11012 },
  { month: 'Jun', active: 12000, new: 700, returning: 11300 },
];

// Data for Feature Adoption Distribution pie chart
const featureAdoptionData = [
  { name: 'Onboarding', value: 35, color: '#4285f4' },
  { name: 'Tutorials', value: 25, color: '#34a853' },
  { name: 'Help Center', value: 20, color: '#fbbc04' },
  { name: 'Live Chat', value: 12, color: '#ea4335' },
  { name: 'Others', value: 8, color: '#9aa0a6' },
];

// Data for User Satisfaction Ratings
const satisfactionData = [
  { category: 'Excellent (5★)', value: 77, color: '#34a853' },
  { category: 'Good (4★)', value: 16, color: '#fbbc04' },
  { category: 'Average (3★)', value: 5, color: '#ff9800' },
  { category: 'Poor (2★)', value: 1, color: '#f44336' },
  { category: 'Very Poor (1★)', value: 1, color: '#d32f2f' },
];

// Data for Dogma Satisfaction Trend
const dogmaSatisfactionData = [
  { month: 'Jan', satisfaction: 4.2 },
  { month: 'Feb', satisfaction: 4.3 },
  { month: 'Mar', satisfaction: 4.4 },
  { month: 'Apr', satisfaction: 4.5 },
  { month: 'May', satisfaction: 4.6 },
  { month: 'Jun', satisfaction: 4.7 },
];

const kpis = [
  {
    label: 'Active Users',
    value: '34,521',
    change: '+12.5%',
    sub: 'Monthly active users',
    icon: <GroupIcon sx={{ color: '#2196f3' }} />,
    color: '#e3f2fd',
  },
  {
    label: 'Guide Completion',
    value: '78.2%',
    change: '****%',
    sub: 'Average completion rate',
    icon: <CheckCircleIcon sx={{ color: '#43a047' }} />,
    color: '#e8f5e9',
  },
  {
    label: 'New Interactions',
    value: '12,847',
    change: '+18.7%',
    sub: 'Recent user interactions',
    icon: <BoltIcon sx={{ color: '#8e24aa' }} />,
    color: '#f3e5f5',
  },
  {
    label: 'Overall Satisfaction',
    value: '4.6/5',
    change: '+0.3',
    sub: 'Average user rating',
    icon: <StarIcon sx={{ color: '#ffb300' }} />,
    color: '#fff8e1',
  },
];

const AnalyticsDashboard = () => {
  const [selectedTab, setSelectedTab] = useState(0);

  const tabLabels = [
    'Overview',
    'Feedback',
  ];

  return (
    <div className='qadpt-web'>
      <div className='qadpt-webcontent'>
        <Container maxWidth="xl" sx={{ background: '#f6f9ff', minHeight: '100vh', py: 3 }}>
      {/* Header */}
      <Box display="flex" alignItems="center" justifyContent="space-between" mb={4}>
        <Box display="flex" alignItems="center" gap={2}>
          <Typography variant="h5" fontWeight={700} color="#2d2357">Digital Adoption Platform</Typography>
          <Typography variant="body2" color="#7b809a">Dashboard • Data • Reports • Analytics</Typography>
        </Box>
        <Box display="flex" alignItems="center" gap={2}>
          <Box sx={{ bgcolor: '#ede7f6', px: 2, py: 0.5, borderRadius: 2, display: 'flex', alignItems: 'center' }}>
            <SearchIcon sx={{ color: '#7c4dff', mr: 1 }} />
            <InputBase placeholder="Search..." sx={{ fontSize: 14, width: 120 }} />
          </Box>
          <IconButton>
            <Badge badgeContent={3} color="error">
              <NotificationsIcon />
            </Badge>
          </IconButton>
          <Avatar sx={{ bgcolor: '#4285f4', color: '#fff', width: 36, height: 36 }}><PersonIcon /></Avatar>
        </Box>
      </Box>

      {/* Tabs Navigation */}
      <Tabs
        value={selectedTab}
        onChange={(e, newValue) => setSelectedTab(newValue)}
        indicatorColor="primary"
        textColor="primary"
        variant="fullWidth"
        sx={{ mb: 3, width: '100%', background: '#fff', borderRadius: 2, boxShadow: 1 }}
      >
        {tabLabels.map((label, idx) => (
          <Tab key={label} label={label} />
        ))}
      </Tabs>

      {/* Tab content rendering */}
      {selectedTab === 0 && (
        <React.Fragment>
          {/* Overview content */}
          {/* KPI Section */}
          <Grid container spacing={3} mb={4}>
            {kpis.map((kpi, idx) => (
              <Grid item xs={12} sm={6} md={3} key={kpi.label}>
                <Card sx={{ p: 2.5, borderRadius: 2, boxShadow: 1, bgcolor: '#fff', minHeight: 120 }}>
                  <Box>
                    <Typography variant="subtitle2" color="#7b809a" mb={1}>{kpi.label}</Typography>
                    <Typography variant="h4" fontWeight={700} color="#2d2357" mb={0.5}>{kpi.value}</Typography>
                    <Typography variant="caption" color="#7b809a">{kpi.sub}</Typography>
                  </Box>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Charts Section */}
          <Grid container spacing={3}>
            {/* User Activity Trends */}
            <Grid item xs={12} md={6}>
              <Card sx={{ p: 3, borderRadius: 2, minHeight: 300, boxShadow: 1 }}>
                <Typography fontWeight={600} mb={2}>User Activity Trends</Typography>
                <ResponsiveContainer width="100%" height={220}>
                  <LineChart data={userActivityData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <RechartsTooltip />
                    <Line type="monotone" dataKey="active" stroke="#4285f4" strokeWidth={2} name="Active Users" />
                    <Line type="monotone" dataKey="new" stroke="#34a853" strokeWidth={2} name="New Users" />
                    <Line type="monotone" dataKey="returning" stroke="#fbbc04" strokeWidth={2} name="Returning Users" />
                  </LineChart>
                </ResponsiveContainer>
              </Card>
            </Grid>

            {/* Feature Adoption Distribution */}
            <Grid item xs={12} md={6}>
              <Card sx={{ p: 3, borderRadius: 2, minHeight: 300, boxShadow: 1 }}>
                <Typography fontWeight={600} mb={2}>Feature Adoption Distribution</Typography>
                <ResponsiveContainer width="100%" height={220}>
                  <PieChart>
                    <Pie
                      data={featureAdoptionData}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {featureAdoptionData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <RechartsTooltip formatter={(value) => [`${value}%`, 'Usage']} />
                  </PieChart>
                </ResponsiveContainer>
                <Box display="flex" flexWrap="wrap" gap={1} mt={2}>
                  {featureAdoptionData.map((item, index) => (
                    <Box key={index} display="flex" alignItems="center" gap={0.5}>
                      <Box width={12} height={12} bgcolor={item.color} borderRadius="50%" />
                      <Typography variant="caption">{item.name}</Typography>
                    </Box>
                  ))}
                </Box>
              </Card>
            </Grid>
          </Grid>

          {/* Second row of charts */}
          <Grid container spacing={3} mt={2}>
            {/* User Satisfaction Ratings */}
            <Grid item xs={12} md={6}>
              <Card sx={{ p: 3, borderRadius: 2, minHeight: 300, boxShadow: 1 }}>
                <Typography fontWeight={600} mb={2}>User Satisfaction Ratings</Typography>
                <ResponsiveContainer width="100%" height={220}>
                  <BarChart data={satisfactionData} layout="horizontal" margin={{ top: 10, right: 30, left: 60, bottom: 0 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="category" type="category" width={80} />
                    <RechartsTooltip formatter={(value) => [`${value}%`, 'Rating']} />
                    <Bar dataKey="value" fill="#4285f4" />
                  </BarChart>
                </ResponsiveContainer>
                <Box display="flex" justifyContent="space-around" mt={2}>
                  <Box textAlign="center">
                    <Typography fontWeight={700} color="#34a853" variant="h6">77%</Typography>
                    <Typography variant="caption" color="#7b809a">Positive</Typography>
                  </Box>
                  <Box textAlign="center">
                    <Typography fontWeight={700} color="#fbbc04" variant="h6">16%</Typography>
                    <Typography variant="caption" color="#7b809a">Neutral</Typography>
                  </Box>
                  <Box textAlign="center">
                    <Typography fontWeight={700} color="#ea4335" variant="h6">7%</Typography>
                    <Typography variant="caption" color="#7b809a">Negative</Typography>
                  </Box>
                </Box>
              </Card>
            </Grid>

            {/* Dogma Satisfaction Trend */}
            <Grid item xs={12} md={6}>
              <Card sx={{ p: 3, borderRadius: 2, minHeight: 300, boxShadow: 1 }}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography fontWeight={600}>Dogma Satisfaction Trend</Typography>
                  <Typography variant="body2" color="#7b809a">Monthly</Typography>
                </Box>
                <ResponsiveContainer width="100%" height={220}>
                  <LineChart data={dogmaSatisfactionData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis domain={[4.0, 5.0]} />
                    <RechartsTooltip formatter={(value) => [`${value}/5`, 'Satisfaction']} />
                    <Line type="monotone" dataKey="satisfaction" stroke="#8e24aa" strokeWidth={3} dot={{ r: 5, fill: '#8e24aa' }} />
                  </LineChart>
                </ResponsiveContainer>
                <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
                  <Box>
                    <Typography variant="body2" color="#8e24aa">Current Score Rating</Typography>
                    <Typography variant="body2" color="#7b809a">Monthly Improvement</Typography>
                  </Box>
                  <Box textAlign="right">
                    <Typography fontWeight={700} color="#2d2357" variant="h6">4.75</Typography>
                    <Typography variant="body2" color="#34a853">+0.1</Typography>
                  </Box>
                </Box>
              </Card>
            </Grid>
          </Grid>
        </React.Fragment>
      )}
      {selectedTab === 1 && (
        <React.Fragment>
          {/* Feedback content */}
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Card sx={{ p: 3, borderRadius: 2, boxShadow: 1 }}>
                <Typography variant="h6" fontWeight={600} mb={2}>User Feedback & Reviews</Typography>
                <Typography variant="body2" color="#7b809a" mb={3}>
                  Detailed feedback analysis and user sentiment tracking
                </Typography>

                {/* Feedback metrics */}
                <Grid container spacing={3} mb={3}>
                  <Grid item xs={12} md={3}>
                    <Box textAlign="center" p={2} bgcolor="#e8f5e9" borderRadius={2}>
                      <Typography variant="h4" fontWeight={700} color="#34a853">4.6</Typography>
                      <Typography variant="body2" color="#7b809a">Average Rating</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Box textAlign="center" p={2} bgcolor="#e3f2fd" borderRadius={2}>
                      <Typography variant="h4" fontWeight={700} color="#4285f4">1,247</Typography>
                      <Typography variant="body2" color="#7b809a">Total Reviews</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Box textAlign="center" p={2} bgcolor="#fff3e0" borderRadius={2}>
                      <Typography variant="h4" fontWeight={700} color="#ff9800">89%</Typography>
                      <Typography variant="body2" color="#7b809a">Positive Feedback</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Box textAlign="center" p={2} bgcolor="#fce4ec" borderRadius={2}>
                      <Typography variant="h4" fontWeight={700} color="#e91e63">24h</Typography>
                      <Typography variant="body2" color="#7b809a">Response Time</Typography>
                    </Box>
                  </Grid>
                </Grid>

                {/* Recent feedback */}
                <Typography variant="h6" fontWeight={600} mb={2}>Recent Feedback</Typography>
                <Box>
                  {[
                    { user: 'Sarah M.', rating: 5, comment: 'Excellent platform! Very intuitive and helpful.', time: '2 hours ago' },
                    { user: 'John D.', rating: 4, comment: 'Great features, could use better mobile support.', time: '5 hours ago' },
                    { user: 'Emma L.', rating: 5, comment: 'Love the new onboarding process!', time: '1 day ago' },
                  ].map((feedback, index) => (
                    <Box key={index} mb={2} p={2} bgcolor="#f8f9fa" borderRadius={2}>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                        <Box display="flex" alignItems="center" gap={1}>
                          <Typography fontWeight={600}>{feedback.user}</Typography>
                          <Box display="flex">
                            {[...Array(5)].map((_, i) => (
                              <StarIcon key={i} sx={{ fontSize: 16, color: i < feedback.rating ? '#ffb300' : '#e0e0e0' }} />
                            ))}
                          </Box>
                        </Box>
                        <Typography variant="caption" color="#7b809a">{feedback.time}</Typography>
                      </Box>
                      <Typography variant="body2" color="#2d2357">{feedback.comment}</Typography>
                    </Box>
                  ))}
                </Box>
              </Card>
            </Grid>
          </Grid>
        </React.Fragment>
      )}
        </Container>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;