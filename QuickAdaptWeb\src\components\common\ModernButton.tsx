import React from 'react';
import { Button, CircularProgress } from '@mui/material';
import { styled } from '@mui/material/styles';

interface ModernButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
}

const StyledButton = styled(Button)<{
  buttonVariant: string;
  buttonSize: string;
}>(({ theme, buttonVariant, buttonSize }) => ({
  borderRadius: 'var(--radius-md)',
  fontWeight: 'var(--font-weight-medium)',
  transition: 'var(--transition-fast)',
  textTransform: 'none',
  border: '1px solid transparent',
  
  // Size variants
  ...(buttonSize === 'sm' && {
    padding: 'var(--spacing-2) var(--spacing-3)',
    fontSize: 'var(--font-size-sm)',
    minHeight: '32px',
  }),
  ...(buttonSize === 'md' && {
    padding: 'var(--spacing-2) var(--spacing-4)',
    fontSize: 'var(--font-size-sm)',
    minHeight: '40px',
  }),
  ...(buttonSize === 'lg' && {
    padding: 'var(--spacing-3) var(--spacing-6)',
    fontSize: 'var(--font-size-base)',
    minHeight: '48px',
  }),
  
  // Variant styles
  ...(buttonVariant === 'primary' && {
    backgroundColor: 'var(--color-primary-600)',
    color: 'var(--color-white)',
    '&:hover': {
      backgroundColor: 'var(--color-primary-700)',
      boxShadow: 'var(--shadow-md)',
    },
    '&:active': {
      backgroundColor: 'var(--color-primary-700)',
      transform: 'translateY(1px)',
    },
    '&:disabled': {
      backgroundColor: 'var(--color-gray-300)',
      color: 'var(--color-gray-500)',
    },
  }),
  
  ...(buttonVariant === 'secondary' && {
    backgroundColor: 'var(--color-gray-100)',
    color: 'var(--color-gray-700)',
    '&:hover': {
      backgroundColor: 'var(--color-gray-200)',
      boxShadow: 'var(--shadow-sm)',
    },
    '&:active': {
      backgroundColor: 'var(--color-gray-300)',
      transform: 'translateY(1px)',
    },
    '&:disabled': {
      backgroundColor: 'var(--color-gray-100)',
      color: 'var(--color-gray-400)',
    },
  }),
  
  ...(buttonVariant === 'outline' && {
    backgroundColor: 'transparent',
    color: 'var(--color-primary-600)',
    borderColor: 'var(--color-primary-600)',
    '&:hover': {
      backgroundColor: 'var(--color-primary-50)',
      borderColor: 'var(--color-primary-700)',
      color: 'var(--color-primary-700)',
    },
    '&:active': {
      backgroundColor: 'var(--color-primary-100)',
      transform: 'translateY(1px)',
    },
    '&:disabled': {
      borderColor: 'var(--color-gray-300)',
      color: 'var(--color-gray-400)',
    },
  }),
  
  ...(buttonVariant === 'ghost' && {
    backgroundColor: 'transparent',
    color: 'var(--color-gray-600)',
    '&:hover': {
      backgroundColor: 'var(--color-gray-100)',
      color: 'var(--color-gray-700)',
    },
    '&:active': {
      backgroundColor: 'var(--color-gray-200)',
      transform: 'translateY(1px)',
    },
    '&:disabled': {
      color: 'var(--color-gray-400)',
    },
  }),
  
  ...(buttonVariant === 'danger' && {
    backgroundColor: 'var(--color-error-600)',
    color: 'var(--color-white)',
    '&:hover': {
      backgroundColor: 'var(--color-error-700)',
      boxShadow: 'var(--shadow-md)',
    },
    '&:active': {
      backgroundColor: 'var(--color-error-700)',
      transform: 'translateY(1px)',
    },
    '&:disabled': {
      backgroundColor: 'var(--color-gray-300)',
      color: 'var(--color-gray-500)',
    },
  }),
}));

const ModernButton: React.FC<ModernButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  fullWidth = false,
  startIcon,
  endIcon,
  onClick,
  type = 'button',
  className,
}) => {
  return (
    <StyledButton
      buttonVariant={variant}
      buttonSize={size}
      disabled={disabled || loading}
      fullWidth={fullWidth}
      startIcon={loading ? <CircularProgress size={16} color="inherit" /> : startIcon}
      endIcon={!loading ? endIcon : undefined}
      onClick={onClick}
      type={type}
      className={className}
      disableElevation
    >
      {children}
    </StyledButton>
  );
};

export default ModernButton;
