export type UserDetails = {
	FirstName: string;
	LastName: string;
	UserName: string;
	EmailId: string;
	Password: string;
	ContactNumber: string;
	Gender: string;
	DateofBirth: string;
	AdminDeactivated: string;
	EmailConfirmed: boolean;
	LoginType: string;
	ProfilePhoto: string;
	RTL: boolean;
	TimeZone: string;
	UserId: string;
	UserType: string;
};

// Define the type for the state setter
export type SetUserDetails = React.Dispatch<React.SetStateAction<UserDetails>>;
