import React, { useState } from 'react';
import { Con<PERSON><PERSON>, TextField, Button, Typography, Box, Link, IconButton, InputAdornment } from '@mui/material';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { Resetpassword, encryptResetPassword } from '../../services/ResetpasswordService';
import { useLocation } from 'react-router-dom';
import { useSnackbar } from "../../SnackbarContext";
import FormHelperText from '@mui/material/FormHelperText';


const ResetPassword = () => {
    const location = useLocation();
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [passwordError, setPasswordError] = useState('');
    const [confirmPasswordError, setConfirmPasswordError] = useState('');
    const [passwordValidations, setPasswordValidations] = useState({
        minLength: false,
        hasUppercase: false,
        hasSpecialChar: false,
        hasNumber: false,
        noSpaces: true,
    });
    const [passwordsMatch, setPasswordsMatch] = useState(false);
    const [isPasswordChanged, setIsPasswordChanged] = useState(false);

    const { openSnackbar } = useSnackbar();
    const pathname = location.pathname;
    const segments = pathname.split('/');
    const passwordLogId = segments[segments.length - 1];

    // Validate password criteria
    const validatePassword = (password: string) => {
        const validations = {
            minLength: password.length >= 8,
            hasUppercase: /[A-Z]/.test(password),
            hasSpecialChar: /[!@#$%^&*]/.test(password),
            hasNumber: /\d/.test(password),
            noSpaces: !/\s/.test(password),
        };
        setPasswordValidations(validations);
        setPasswordsMatch(password === confirmPassword);
        return validations.minLength && validations.hasUppercase && validations.hasSpecialChar && validations.hasNumber && validations.noSpaces;
    };

    // Password change handler with validation
    const handlePasswordChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const newPassword = event.target.value;
        setPassword(newPassword);
        const isValid = validatePassword(newPassword);
        if (!isValid) {
            setPasswordError(
                !passwordValidations.noSpaces
                    ? "Spaces are not accepted."
                    : "Password must be at least 8 characters, contain 1 uppercase letter, 1 special character, 1 number, and no spaces."
            );
        } else {
            setPasswordError('');
        }
        setPasswordsMatch(newPassword === confirmPassword);
    };

    // Confirm password change handler with validation
    const handleConfirmPasswordChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const newConfirmPassword = event.target.value;
        setConfirmPassword(newConfirmPassword);
        if (password !== newConfirmPassword) {
            setConfirmPasswordError("Passwords do not match.");
        } else {
            setConfirmPasswordError('');
        }
        setPasswordsMatch(password === newConfirmPassword && password.length >= 8);
    };

    const handleSubmit = async () => {
        let isValid = true;
        if (!password) {
            setPasswordError('This field is required.');
            isValid = false;
        }
        if (!confirmPassword) {
            setConfirmPasswordError('This field is required.');
            isValid = false;
        }
        if (isValid && passwordsMatch && !passwordError && !confirmPasswordError) {
            const encryptedPassword = encryptResetPassword(password);
            const encryptedReenteredPassword = encryptResetPassword(confirmPassword);
            if (encryptedPassword && encryptedReenteredPassword) {
                try {
                    const response = await Resetpassword(passwordLogId, encryptedPassword, encryptedReenteredPassword);
                    if (response && response.Success) {
                        setIsPasswordChanged(true);
                    } else {
                        openSnackbar(response, "error");
                    }
                } catch (error) {
                    console.error('Error during password reset:', error);
                }
            } else {
                console.error('Encryption failed. Please try again.');
            }
        }
        localStorage.clear();
      document.cookie.split(";").forEach(cookie => {
        const [name] = cookie.split("=");
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
      });
      localStorage.setItem('logout-event', Date.now().toString());
        sessionStorage.clear();
    };
    const disabled = !passwordsMatch || passwordError !== '' || confirmPasswordError !== '';
    return (
        <Container maxWidth="xs" className="qadpt-resetpassword">
            <Box mb={4} className="qadpt-brand-logo">
                <Typography variant="h3" className="qadpt-brand-logo-text" gutterBottom>
                    QuickAdopt
                </Typography>
            </Box>
            <Box>
                {!isPasswordChanged ? (
                    <>
                        <Box className="qadpt-welcome-message">
                            <Typography variant="h4" className="qadpt-welcome-message-text">
                                Reset your password
                            </Typography>
                            <Typography
                                variant="body2"
                                color="textSecondary"
                                align="center"
                                mb={2}
                                sx={{
                                    width: "325px",
                                    height: "38px",
                                    opacity: "0.8",
                                    fontFamily: "Poppins",
                                    fontSize: "14px",
                                    fontWeight: 400,
                                    lineHeight: "19px",
                                    textAlign: "center",
                                    color: "#222222",
                                }}
                            >
                                Enter your new password below to change your password.
                            </Typography>
                        </Box>

                        <Box className="qadpt-login-form">
                            <Typography className="qadpt-form-label">New Password</Typography>
                            <TextField
                                margin="normal"
                                required
                                fullWidth
                                type={showPassword ? "text" : "password"}
                                id="new-password"
                                name="new-password"
                                autoComplete="new-password"
                                autoFocus
                                value={password}
                                onChange={handlePasswordChange}
                                placeholder="min. 8 characters"
                                className="qadpt-custom-input"
                                error={!!passwordError}
                                InputProps={{
                                    endAdornment: (
                                        <InputAdornment position="end">
                                            <IconButton
                                                aria-label="toggle password visibility"
                                                onClick={() => setShowPassword(!showPassword)}
                                                edge="end"
                                            >
                                                {showPassword ? <VisibilityOff /> : <Visibility />}
                                            </IconButton>
                                        </InputAdornment>
                                    ),
                                }}
                            />
                            {passwordError ? (
                                    <FormHelperText sx={{ color: 'red', fontSize: '12px' }}>
                                        {passwordError}
                                       
                                    </FormHelperText>
                            ):""}

                            <Typography className="qadpt-form-label">Re-enter New Password</Typography>
                            <TextField
                                margin="normal"
                                required
                                fullWidth
                                type={showConfirmPassword ? "text" : "password"}
                                id="confirm-password"
                                name="confirm-password"
                                autoComplete="confirm-password"
                                autoFocus
                                value={confirmPassword}
                                onChange={handleConfirmPasswordChange}
                                placeholder="Same as above"
                                className="qadpt-custom-input"
                                error={!!confirmPasswordError}
                                //helperText={confirmPasswordError}
                                InputProps={{
                                    endAdornment: (
                                        <InputAdornment position="end">
                                            <IconButton
                                                aria-label="toggle confirm password visibility"
                                                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                                edge="end"
                                            >
                                                {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                                            </IconButton>
                                        </InputAdornment>
                                    ),
                                }}
                                  
                            />
                            {confirmPasswordError ? (
                                    <FormHelperText sx={{ color: 'red', fontSize: '12px' }}>
                                        {confirmPasswordError}
                                    </FormHelperText>
                            ):""}

                            {(password || confirmPassword) && (
                                <Box className="qadpt-passwordhint">
                                    <Typography className="qadpt-passwordhint-text">Your password must contain</Typography>
                                    <Box className="qadpt-passwordhint-container">
                                        <CheckCircleIcon className={passwordValidations.minLength ? "qadpt-checkicon-valid" : ""} />
                                        <Typography className="qadpt-passwordhint-item">At least 8 characters</Typography>
                                    </Box>
                                    {/* <Box className="qadpt-passwordhint-container">
                                        <CheckCircleIcon className={passwordValidations.hasUppercase ? "qadpt-checkicon-valid" : ""} />
                                        <Typography className="qadpt-passwordhint-item">1 capital letter</Typography>
                                    </Box>
                                    <Box className="qadpt-passwordhint-container">
                                        <CheckCircleIcon className={passwordValidations.hasSpecialChar ? "qadpt-checkicon-valid" : ""} />
                                        <Typography className="qadpt-passwordhint-item">1 special character</Typography>
                                    </Box>
                                    <Box className="qadpt-passwordhint-container">
                                        <CheckCircleIcon className={passwordValidations.hasNumber ? "qadpt-checkicon-valid" : ""} />
                                        <Typography className="qadpt-passwordhint-item">1 number</Typography>
                                    </Box> */}
                                </Box>
                            )}

                            <Button
                                type="button"
                                fullWidth
                                variant="contained"
                                onClick={handleSubmit}
                                disabled={!passwordsMatch || passwordError !== '' || confirmPasswordError !== ''}
                                className={disabled ? "qadpt-btn-disabled" : "qadpt-btn-default"}
                            >
                                Reset password
                            </Button>
                        </Box>

                        <Box mt={12} className="qadpt-login-footer">
                            <Typography variant="body2" className="qadpt-footer-text">
                                <Link href="/terms-of-use" className="qadpt-footer-link">
                                    Terms of use
                                </Link>{" "}
                                |{" "}
                                <Link href="/privacy-policy" className="qadpt-footer-link">
                                    Privacy Policy
                                </Link>
                            </Typography>
                        </Box>
                    </>
                ) : (
                    <Box className="qadpt-pwd-changed">
                        <Typography variant="h5" className="qadpt-pwd-title">
                            Password Changed!
                        </Typography>
                        <Typography variant="body2" color="textSecondary" className="qadpt-changed-msg" mt="10px">
                            Your password has been changed successfully.
                        </Typography>
                        <Button
                            type="button"
                            variant="contained"
                            className="qadpt-btn-default"
                            href="https://app.quickadopt.in/Login"
                        >
                            Back to QuickAdopt Platform
                        </Button>
                    </Box>
                )}
                <Box mt={12} className="qadpt-login-footer">
                    <Typography variant="body2" className="qadpt-footer-text">
                        <Link href="/terms-of-use" className="qadpt-footer-link">
                            Terms of use
                        </Link>{" "}
                        |{" "}
                        <Link href="/privacy-policy" className="qadpt-footer-link">
                            Privacy Policy
                        </Link>
                    </Typography>
                </Box>
            </Box>
        </Container>
    );
};

export default ResetPassword;
