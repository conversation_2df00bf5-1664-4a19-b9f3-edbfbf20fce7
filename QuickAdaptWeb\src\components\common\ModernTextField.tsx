import React from 'react';
import { TextField, TextFieldProps } from '@mui/material';
import { styled } from '@mui/material/styles';

interface ModernTextFieldProps extends Omit<TextFieldProps, 'variant'> {
  variant?: 'outlined' | 'filled';
}

const StyledTextField = styled(TextField)({
  '& .MuiOutlinedInput-root': {
    borderRadius: 'var(--radius-md)',
    backgroundColor: 'var(--color-white)',
    fontSize: 'var(--font-size-sm)',
    transition: 'var(--transition-fast)',
    
    '& fieldset': {
      borderColor: 'var(--color-gray-300)',
      borderWidth: '1px',
    },
    
    '&:hover fieldset': {
      borderColor: 'var(--color-gray-400)',
    },
    
    '&.Mui-focused fieldset': {
      borderColor: 'var(--color-primary-500)',
      borderWidth: '2px',
    },
    
    '&.Mui-error fieldset': {
      borderColor: 'var(--color-error-500)',
    },
    
    '&.Mui-disabled': {
      backgroundColor: 'var(--color-gray-50)',
      '& fieldset': {
        borderColor: 'var(--color-gray-200)',
      },
    },
  },
  
  '& .MuiFilledInput-root': {
    borderRadius: 'var(--radius-md)',
    backgroundColor: 'var(--color-gray-50)',
    fontSize: 'var(--font-size-sm)',
    border: '1px solid var(--color-gray-200)',
    transition: 'var(--transition-fast)',
    
    '&:before, &:after': {
      display: 'none',
    },
    
    '&:hover': {
      backgroundColor: 'var(--color-gray-100)',
      borderColor: 'var(--color-gray-300)',
    },
    
    '&.Mui-focused': {
      backgroundColor: 'var(--color-white)',
      borderColor: 'var(--color-primary-500)',
      borderWidth: '2px',
    },
    
    '&.Mui-error': {
      borderColor: 'var(--color-error-500)',
    },
  },
  
  '& .MuiInputLabel-root': {
    fontSize: 'var(--font-size-sm)',
    fontWeight: 'var(--font-weight-medium)',
    color: 'var(--color-gray-700)',
    
    '&.Mui-focused': {
      color: 'var(--color-primary-600)',
    },
    
    '&.Mui-error': {
      color: 'var(--color-error-600)',
    },
  },
  
  '& .MuiFormHelperText-root': {
    fontSize: 'var(--font-size-xs)',
    marginTop: 'var(--spacing-1)',
    
    '&.Mui-error': {
      color: 'var(--color-error-600)',
    },
  },
  
  '& .MuiInputBase-input': {
    padding: 'var(--spacing-3) var(--spacing-4)',
    fontSize: 'var(--font-size-sm)',
    color: 'var(--color-gray-900)',
    
    '&::placeholder': {
      color: 'var(--color-gray-400)',
      opacity: 1,
    },
    
    '&.Mui-disabled': {
      color: 'var(--color-gray-500)',
    },
  },
});

const ModernTextField: React.FC<ModernTextFieldProps> = ({
  variant = 'outlined',
  ...props
}) => {
  return (
    <StyledTextField
      variant={variant}
      {...props}
    />
  );
};

export default ModernTextField;
