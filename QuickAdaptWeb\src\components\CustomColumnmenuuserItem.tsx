import React, { useState, useEffect } from 'react';
import TextField from '@mui/material/TextField';
import SearchIcon from '@mui/icons-material/Search';
import IconButton from '@mui/material/IconButton';
import ClearIcon from '@mui/icons-material/Clear';
import Autocomplete from '@mui/material/Autocomplete';
import Checkbox from '@mui/material/Checkbox';
import { MenuItem, ListItemText } from '@mui/material';
import Chip from '@mui/material';
 
const CustomColumnMenuUserItem: React.FC<{ options: string[]; onSearch: (value: string[]) => void }> = ({
	options,
	onSearch,
}) => {
	const [searchText, setSearchText] = useState("");
	const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
	const [filteredOptions, setFilteredOptions] = useState<string[]>(options);

	useEffect(() => {
		setFilteredOptions(options.filter((option) => option.toLowerCase().includes(searchText.toLowerCase())));
	}, [searchText, options]);

	const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		setSearchText(event.target.value);
		// onSearch([event.target.value]);
	};

	const handleClearSearch = () => {
		setSearchText("");
		setSelectedOptions([]);
		onSearch([]);
	};

	const handleSelectAll = () => {
		const newSelectedOptions = selectedOptions.length === filteredOptions.length ? [] : filteredOptions;
		setSelectedOptions(newSelectedOptions);
		onSearch(newSelectedOptions);
	};

	const handleOptionChange = (event: React.SyntheticEvent, newValue: string[]) => {
		setSelectedOptions(newValue);
		onSearch(newValue);
	};

	const handleSearchClick = () => {
		onSearch([searchText]);
	};

	return (
		<div style={{ padding: "8px 12px", display: "flex", flexDirection: "column" }}>
			<Autocomplete
				multiple
				options={filteredOptions}
				disableCloseOnSelect
				value={selectedOptions}
				onChange={handleOptionChange}
				renderOption={(props, option, { selected }) => (
					<>
						{option === filteredOptions[0] && (
							<MenuItem
								key="select-all"
								onClick={handleSelectAll}
							>
								<Checkbox
									checked={selectedOptions.length === filteredOptions.length}
									indeterminate={selectedOptions.length > 0 && selectedOptions.length < filteredOptions.length}
								/>
								<ListItemText primary="Select All" />
							</MenuItem>
						)}
						<MenuItem {...props}>
							<Checkbox checked={selected} />
							{option}
						</MenuItem>
					</>
				)}
				renderInput={(params) => (
					<TextField
						{...params}
						variant="standard"
						label="Search"
						placeholder="Search..."
						value={searchText}
						onChange={handleSearchChange}
						InputProps={{
							...params.InputProps,
							endAdornment: (
								<>
									<IconButton onClick={handleClearSearch}>
										<ClearIcon />
									</IconButton>
								</>
							),
						}}
					/>
				)}
			/>
		</div>
	);
};
 
export default CustomColumnMenuUserItem;