import React, { useState, useEffect } from "react";
import CreateAccount from "../account/AccountCreate";
import {
	GridColDef,
	GridRenderCellParams,
	GridToolbarContainer,
	GridToolbarColumnsButton,
	GridToolbarFilterButton,
	GridToolbarDensitySelector,
	DataGrid,
	GridPaginationModel,
	GridColumnMenuProps,
} from "@mui/x-data-grid";
import { Button, Menu, MenuItem, FormControlLabel, IconButton, Snackbar, Alert, Box, TextField,	Container, Tooltip, } from "@mui/material";
import { useNavigate } from "react-router-dom";
import BorderColorOutlinedIcon from "@mui/icons-material/BorderColorOutlined";
import AssignmentOutlinedIcon from '@mui/icons-material/AssignmentOutlined';
import DeleteIcon from "@mui/icons-material/Delete";
import MailIcon from "@mui/icons-material/Mail";
import SaveAltIcon from "@mui/icons-material/SaveAlt";
import loader from "../../assets/loader.gif";
import CustomGrid from "../common/Grid";
import MarkEmailReadIcon from "@mui/icons-material/MarkEmailRead";
import EditAccount from "../account/AccountEdit";
import { GetAllAccounts, fetchDeleteAccountDetails, GetAccountsList } from "../../services/AccountService";
import { GetSystemPromptsList } from "../../services/SystemPromtServices";
import { isSidebarOpen, subscribe } from "../adminMenu/sidemenustate";
import { organizationsList } from "../organization/orgData";
import CustomColumnMenu from "../CustomColumnMenu";
import AccountCustomColumnMenu from "../account/AccountsColumnMenu";
import { useSnackbar } from "../../SnackbarContext";
import styles from "../account/AccountStle.module.scss";
import { OrganizationId } from "../common/Home";
import FilterPopup from "../settings/Filterpopup";
import InputAdornment from "@mui/material/InputAdornment";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import { useTranslation } from "react-i18next";
import AddBoxIcon from '@mui/icons-material/AddBox';
import  Delete  from "../../assets/icons/delete.svg";

type InputFields = {
	AccountName: string;
	SearchInput: string;
};
type ErrorFields = Partial<InputFields>;
interface CustomDataGridProps extends React.ComponentProps<typeof DataGrid> {
	components?: {
		columnMenu?: React.ComponentType<GridColumnMenuProps>;
	};
}

interface Model {
	AccountId: string;
	BotName: string;
	CreatedBy: string;
	CreatedDate: string;
	Id: string;
	OrganizationId: string;
	SystemPrompt: string;
	UpdatedBy: string;
	UpdatedDate: string;
}

let AccountIds = "********-*********-e99c7653-d095-46a8-aef4-7008f293d33f";
let OrganizationIds = "********-*********-df49ef77-08c7-4d7b-b085-06255181b55a";

const AgentsList = () => {
	const { t: translate } = useTranslation();
	const [showPopup, setShowPopup] = useState(false);
	const [models, setModels] = useState<Model[]>([]);
	const [loading, setLoading] = useState(true);
	const [accountidedit, setAccountIdEdit] = useState("");
	const [showeditPopup, setShowEditPopup] = useState(false);
	const [showDeletePopup, setShowDeletePopup] = useState(false);
	const [snackbarOpen, setSnackbarOpen] = useState(false);
	const [snackbarMessage, setSnackbarMessage] = useState("");
	const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error">("success");
	const Organizationid = OrganizationId;
	const [skip, setskip] = useState(0);
	const [top, settop] = useState(0);
	const [totalcount, setTotalcount] = useState(0);
	const { openSnackbar } = useSnackbar();
	const [orderByFields, setOrderByFields] = useState("");
	const [inputs, setInputs] = useState<InputFields>({
		AccountName: "",
		SearchInput: "",
	});
	const [sortModel, setSortModel] = useState([]);
	const [filters, setFilters] = useState([]);
	const handleSortModelChange = (model: any) => {
		setSortModel(model);
		const orderByField = model
			.map((item: any) => (item.sort === "desc" ? `${item.field} desc` : item.field))
			.join(", ");

		setOrderByFields(orderByField);
		GetSystemPromptsList(setModels, setLoading, OrganizationIds, AccountIds, skip, top, setTotalcount, orderByField, filters);
	};
	const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({
		page: 0,
		pageSize: 15,
	});
	const [errors, setErrors] = useState<ErrorFields>({
		AccountName: "",
	});
	const [AccountDeleteDetails, setAccountDeleteDetails] = useState({
		AccountId: "",
		AccountName: "",
		AccountType: "",
		CreatedBy: "",
		CreatedDate: "",
		Organizationid: "",
		UpdatedBy: "",
		UpdatedDate: "",
		Active: Boolean(true),
	});

	const FetchSystemPrompts = () => {
		setLoading(true);
		const skipcount = paginationModel.pageSize || 15;
		const limitcount = paginationModel.page * skipcount;
		const skips = limitcount;
		const top = paginationModel.pageSize;
		setskip(skips);
		settop(top);
		GetSystemPromptsList(setModels, setLoading, OrganizationIds, AccountIds, skips, top, setTotalcount, orderByFields, filters);
	};

	useEffect(() => {
		setLoading(true);
		FetchSystemPrompts();
	}, [paginationModel, orderByFields, filters]);

	useEffect(() => {
		if (models.length !== 0) {
			setLoading(false);
		}
	}, [models]);

	useEffect(() => {
		const unsubscribe = subscribe(setSidebarOpen);
		return () => unsubscribe();
	}, []);

	const handleSnackbarClose = () => {
		setSnackbarOpen(false);
	};

	const navigate = useNavigate();
	
	const columns: GridColDef[] = [
		{
			field: "AccountId",
			headerName: translate('Account ID'),
			width: 200,
			flex: 1,
			disableColumnMenu: true,
			resizable: false,
		},
		{
			field: "BotName",
			headerName: translate('Bot Name'),
			width: 150,
			flex: 1,
			disableColumnMenu: true,
			resizable: false,
        },
		{
			field: "Id",
			headerName: translate('ID'),
			width: 200,
			flex: 1,
			disableColumnMenu: true,
			resizable: false,
		},
		{
			field: "OrganizationId",
			headerName: translate('Organization ID'),
			width: 200,
			flex: 1,
			disableColumnMenu: true,
			resizable: false,
		},
        {
			field: "SystemPrompt",
			headerName: translate('System Prompt'),
			width: 300,
			flex: 1,
			disableColumnMenu: true,
			resizable: false,
			renderCell: (params) => {
				const systemPrompt = params.value || '';
				const truncatedText = systemPrompt.length > 50 ? `${systemPrompt.substring(0, 50)}...` : systemPrompt;
				
				return (
					<Tooltip title={systemPrompt} arrow>
						<div>{truncatedText}</div>
					</Tooltip>
				);
			}
		},
		{
			field: "CreatedBy",
			headerName: translate('Created By'),
			width: 150,
			flex: 1,
			disableColumnMenu: true,
			resizable: false,
		},
		{
			field: "CreatedDate",
			headerName: translate('Created Date'),
			width: 150,
			flex: 1,
			disableColumnMenu: true,
			renderCell: (params) => {
				const createdDate = params.value ? new Date(params.value) : null;
				return createdDate ? createdDate.toLocaleDateString('en-GB') : "";
			},
			resizable: false,
		},
		{
			field: "UpdatedBy",
			headerName: translate('Updated By'),
			width: 150,
			flex: 1,
			disableColumnMenu: true,
			resizable: false,
		},
		{
			field: "UpdatedDate",
			headerName: translate('Updated Date'),
			width: 150,
			flex: 1,
			disableColumnMenu: true,
			renderCell: (params) => {
				const updatedDate = params.value ? new Date(params.value) : null;
				return updatedDate ? updatedDate.toLocaleDateString('en-GB') : "";
			},
			resizable: false,
		},
		{
			field: "actions",
			headerName: translate('Actions'),
			sortable: false,
			width: 120,
			flex: 1,
			disableColumnMenu: true,
			resizable: false,
			renderCell: (params) => {
				return (
					<Box className="qadpt-grd-act">
						<Tooltip arrow title={translate('Edit agent')}>
							<BorderColorOutlinedIcon 
								style={{ color: "#7A7B7D" }}
								className={styles.Addiconingrid}
							/>
						</Tooltip>
						<Tooltip arrow title={translate('Scripts')}>
							<AssignmentOutlinedIcon 
								style={{ color: "#7A7B7D" }}
								className={styles.Addiconingrid}
								onClick={() => navigate("/settings/scripts", { state: { agentData: params.row } })}
							/>
						</Tooltip>
					</Box>
				);
			},
		},
	];

	const handleDeleteAccount = () => {
		fetchDeleteAccountDetails(
			accountidedit,
			setLoading,
			setModels,
			setShowDeletePopup,
			Organizationid,
			skip,
			top,
			setTotalcount,
			setSnackbarMessage,
			setSnackbarSeverity,
			setSnackbarOpen,
			openSnackbar,
			orderByFields,
			filters
		);
	};

	const handleSearch = (value: string[]) => {
		// Handle search functionality
	};

	const globalhandleSearch = (searchValue: any) => {
		const newFilter = {
			FieldName: "AccountName",
			ElementType: "string",
			Condition: "contains",
			Value: searchValue,
			IsCustomField: false,
		};
		let skips = 0;
		let top = 0;
		const skipCount = paginationModel.pageSize || 15;
		const limitCount = paginationModel.page * skipCount;
		top = paginationModel.pageSize;
		skips = limitCount;
		setskip(skips);
		settop(top);

		try {
			GetSystemPromptsList(setModels, setLoading, OrganizationIds, AccountIds, skip, top, setTotalcount, orderByFields, [newFilter]);
		} catch (error) {
			console.error("Failed to fetch user roles:", error);
		}
	};
	
	const clearSearch = () => {
		setInputs({ ...inputs, SearchInput: "" });
		GetSystemPromptsList(setModels, setLoading, OrganizationIds, AccountIds, skip, top, setTotalcount, orderByFields, filters);
	};
