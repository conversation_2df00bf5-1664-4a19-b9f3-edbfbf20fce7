
import React, { useState, useEffect } from "react";
import TextField from "@mui/material/TextField";
import Checkbox from "@mui/material/Checkbox";
import { MenuItem, ListItemText, Button, Box, Paper } from "@mui/material";
import FilterAltIcon from "@mui/icons-material/FilterAlt";
import Autocomplete from "@mui/material/Autocomplete";
import { getOrganizations } from "../../services/OrganizationService";
import styles from "./OrganizationStyles.module.scss";

const OrganizationCustomColumnMenuItem: React.FC<{
  column: any;
  skip: any;
  top: any;
  models: any[];
  OrganizationId: any;
  setTotalcount: any;
  orderByFields: any;
  setModels: any;
  sortModel: any;
  setLoading: any;
  filters: any;
  setFilters: any;
  options: string[];
  onSearch: (value: string[]) => void;
  open: boolean;
  hideMenu: (event: React.SyntheticEvent<Element, Event>) => void;
  colDef: any;
  modelsData: any[];
  optionsModel: any;
  paginationModel: any;
}> = ({
  column,
  skip,
  top,
  OrganizationId,
  setTotalcount,
  orderByFields,
  setModels,
  sortModel,
  setLoading,
  filters,
  setFilters,
  options,
  onSearch,
  open,
  hideMenu,
  models,
  colDef,
  modelsData,
  optionsModel,
  paginationModel,
  ...other
}) => {
  const [searchText, setSearchText] = useState("");
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [filteredOptions, setFilteredOptions] = useState<string[]>(options);

  // Maintain a list of all available names
  const [allNames, setAllNames] = useState<string[]>([]);

  useEffect(() => {
    const uniqueOptions = Array.from(new Set(options));
    setFilteredOptions(uniqueOptions);

    // Update all names when the models data changes
    if (column === "Name") {
      if (filters.length) {
        if (filters.length && filters.find((filter: any) => filter.FieldName == 'Type')) {
          const names = Array.from(new Set(models.map((model: any) => model.Name)));
          const allDisplayNames = Array.from(new Set(models.map((model: any) => model.Name)));
          setAllNames(names);
          setSelectedOptions(names);
          setFilteredOptions(names);
        } else {
          const names = Array.from(new Set(models.map((model: any) => model.Name)));
          const allDisplayNames = Array.from(new Set(optionsModel.map((model: any) => model.Name)));
          setAllNames(names);
          setSelectedOptions(names);
          setFilteredOptions(allDisplayNames as string[]);
        }
      } else {
        const names = Array.from(new Set(models.map((model: any) => model.Name)));
        const allDisplayNames = Array.from(new Set(optionsModel.map((model: any) => model.Name)));
        setAllNames(names);
        setSelectedOptions(names);
        setFilteredOptions(allDisplayNames as string[]);  // Show all names in dropdown
      }
      
    }
  }, [options, models, column]);

  useEffect(() => {
    if (column === "Type") {
      if (filters.length) {
        if (Array.isArray(models) && filters.find((filter: any)=> filter.FieldName == 'Name')) {
          const typeOptions = Array.from(new Set(models.map((model: any) => model.Type)));
          setSelectedOptions(typeOptions);
          setFilteredOptions(typeOptions);
        } else {
          const typeOptions = Array.from(new Set(models.map((model: any) => model.Type)));
          const typeOptionsModel = Array.from(new Set(optionsModel.map((model: any) => model.Type)));
          setSelectedOptions(typeOptions as string[]);
          setFilteredOptions(typeOptionsModel as string[]);
        }
      } else {
        if (Array.isArray(optionsModel)) {
          const typeOptions = Array.from(new Set(optionsModel.map((model: any) => model.Type)));
          setSelectedOptions(typeOptions as string[]);
          setFilteredOptions(typeOptions as string[]);
        }
      }
    }
  }, [column, models, allNames]);

  const handleOptionChange = (event: React.ChangeEvent<{}>, value: string[]) => {
    setSelectedOptions(value);
  };

  const handleCancelClick = (event: React.SyntheticEvent<Element, Event>) => {
    hideMenu(event);
  };

  const handleClearFiltersClick = (event: React.SyntheticEvent<Element, Event>) => {
    setSearchText("");
    setFilters([]);
    onSearch([]);
    //getOrganizations(setModels, setLoading, 0, paginationModel.pageSize, setTotalcount, sortModel, []);
    //setSelectedOptions(Array.from(new Set(models.map((model: any) => model.Type))));
    hideMenu(event);
  };

  const handleApplyClick = async (event: React.SyntheticEvent<Element, Event>) => {
    const searchValue = selectedOptions.length ? selectedOptions : [searchText];
    const combinedValue = searchValue.join(",");

    const newFilter = {
      FieldName: column,
      ElementType: "string",
      Condition: "in",
      Value: combinedValue,
      IsCustomField: false,
    };

    const updatedFilters = [...filters];
    const filterIndex = updatedFilters.findIndex(
      (filter) => filter.FieldName === column
    );

    if (filterIndex !== -1) {
      updatedFilters[filterIndex] = newFilter;
    } else {
      updatedFilters.push(newFilter);
    }

    setFilters(updatedFilters);

    await getOrganizations(setModels, setLoading, 0, paginationModel.pageSize, setTotalcount, sortModel, updatedFilters);

    setSelectedOptions(searchValue);
    //setFilteredOptions(allNames);
    onSearch(selectedOptions);
    hideMenu(event);
  };

  const handleSelectAll = () => {
    const newSelectedOptions =
      selectedOptions.length === filteredOptions.length ? [] : filteredOptions;
    setSelectedOptions(newSelectedOptions);
  };

  const isClearFiltersDisabled = !searchText && selectedOptions.length === options.length;
  const isSearchButtonDisabled = selectedOptions.length === 0 && !searchText;
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);


  return (

    <div className="qadpt-org-filter" style={{ position: "relative" }}>
      <Autocomplete
        multiple
        disableCloseOnSelect
        options={filteredOptions}
        value={selectedOptions}
        onChange={handleOptionChange}
        onInputChange={(event, value) => setSearchText(value)}
        onOpen={() => setIsDropdownOpen(true)}
        //onClose={() => setIsDropdownOpen(false)}
        renderOption={(props, option, { selected }) => (
          <div>
            {option === filteredOptions[0] && (
              <MenuItem key="select-all" onClick={handleSelectAll}>
                <Checkbox
                  checked={selectedOptions.length === filteredOptions.length}
                  indeterminate={
                    selectedOptions.length > 0 &&
                    selectedOptions.length < filteredOptions.length
                  }
                />
                <ListItemText primary="Select All" />
              </MenuItem>
            )}
            <MenuItem {...props} key={option}>
              <Checkbox checked={selectedOptions.includes(option)} />
              <ListItemText primary={option} />
            </MenuItem>
          </div>
        )}
        renderInput={(params) => (
          <TextField
            {...params}
            variant="outlined"
            label="Search"
            placeholder="Select Options"
            onKeyDown={(event) => {
              if (["ArrowUp", "ArrowDown", "Enter"].includes(event.key)) {
                event.stopPropagation();
              }
              else
              {
                event.stopPropagation();
              }
            }}
            InputProps={{
              ...params.InputProps,
              endAdornment: (
                <React.Fragment>
                  {params.InputProps.endAdornment}
                </React.Fragment>
              ),
            }}
          />
        )}
        PaperComponent={(props) => (
          <Paper
            {...props}
            style={{
              position: "absolute",
              top: "100%",
              left: 0,
              width: "100%",
              zIndex: 10,
              marginTop: "4px", // Small gap between input and dropdown
            }}
            onWheel={(event) => event.stopPropagation()}
          />
        )}
        ListboxProps={{
          style: { maxHeight: "220px", overflowY: "auto" }, 
        }}
        renderTags={() => null}
      />
    
      <Box
        display="flex"
        justifyContent="flex-end"
        mt={2}
        className="qadpt-btn"
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          padding:"0 10px",
          marginTop: isDropdownOpen ? "250px" : "10px", // Dynamically change marginTop
          transition: "margin-top 0.3s ease-in-out", // Smooth transition
        }}
      >
        <Button variant="outlined" color="primary" onClick={handleApplyClick} disabled={isSearchButtonDisabled}>
          OK
        </Button>
        <Button variant="outlined" color="secondary" onClick={handleCancelClick} style={{ marginLeft: "8px" }}>
          Cancel
        </Button>
        <Button onClick={handleClearFiltersClick} startIcon={<FilterAltIcon />} disabled={isClearFiltersDisabled} style={{ marginLeft: "8px" }}>
          Clear Filters
        </Button>
      </Box>
    </div>
  );
};

export default OrganizationCustomColumnMenuItem;
