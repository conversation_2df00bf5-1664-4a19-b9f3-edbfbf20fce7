import React, { useState, useEffect } from 'react';
import { Con<PERSON>er, Box, Typography, TextField, Button, Link, IconButton, InputAdornment } from '@mui/material';
import Visibility from '@mui/icons-material/Visibility';
import { useAuth } from '../auth/AuthProvider';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import { LoginService } from "../../services/LoginService";
import { GetUserDetails, encryptPassword } from '../../services/UserService';
import { JSEncrypt } from 'jsencrypt';
import { useNavigate } from "react-router-dom";
import { GetUserDetailsById } from '../../services/UserService';
import { LoginUserInfo } from '../../models/LoginUserInfo';
import jwt_decode from "jwt-decode";
import { getAllUsers } from '../../services/UserService';
import { Organization } from "../../models/Organization";
import { User } from "../../models/User";
import { User as Users, UserManager } from 'oidc-client-ts';
import { FormHelperText } from '@mui/material';
import { getOrganizationById } from '../../services/OrganizationService';
import { QuickAdopttext } from "../../assets/icons/icons";
import { checkSessionExpired } from '../../services/APIService';
import { Alert } from '@mui/material';
//import { useAuth } from '../auth/AuthProvider';
let userLocalData: { [key: string]: any } = {}
let SAinitialsData: string;
let userDetails: User;
const Login: React.FC = () => {

  const { user } = useAuth();
    let UserId: string;
    let OrganizationId: string;
    const [showSessionExpiredAlert, setShowSessionExpiredAlert] = useState(false);
  
    useEffect(() => {
        // Check if user was redirected due to session expiration
        const sessionExpired = checkSessionExpired();
        if (sessionExpired) {
          setShowSessionExpiredAlert(true);
          
          // Auto-hide the alert after 5 seconds
          const timer = setTimeout(() => {
            setShowSessionExpiredAlert(false);
          }, 5000);
          
          return () => clearTimeout(timer);
        }
    }, []);
    
    const [showPassword, setShowPassword] = useState(false);
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [users, setUser] = useState<Users | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [loginUserInfo, setLoginUserInfo] = useState<LoginUserInfo | undefined>(undefined);
    const [response, setresponse] = useState('');
    const [userIds, setuserId] = useState("");
    const [organizationDetails, setOrganizationDetails] = useState<Organization | null>(null);
    const [loginUserDetails, setUserDetails] = useState<User | null>(null);
    const handleClickShowPassword = () => {
        setShowPassword(!showPassword);
    };
    const { signOut, loggedOut } = useAuth();

    const handleEmailChange = (event: any) => {
        setEmail(event.target.value);
    };

    const handlePasswordChange = (event: any) => {
        setPassword(event.target.value);
    };
    const navigate = useNavigate();

useEffect(() => {
  const hasSessionExpired = checkSessionExpired();
  const token = localStorage.getItem("access_token");

  if (user && token && !hasSessionExpired) {
    navigate("/", { replace: true });
  }
}, [user]);
    const handleSubmit = async () => {
        try {
            const organizationId = "1";
            const rememberLogin = true;
            const returnUrl = ""
            const authType = "admin"
            const tenantId = "web"
            if (password === '' || password == null) {
                setError('password should not be empty');
            }
            else if (email === '' || email == null) {
                setError('email should not be empty');
            }
            else {
                const isEncryptionEnabled = process.env.REACT_APP_ENABLE_ENCRYPTION === 'true';
                const publicKey = process.env.REACT_APP_PUBLIC_ENCRYPT_KEY || '';
                const encryptor = new JSEncrypt();
                encryptor.setPublicKey(publicKey);
                const now = new Date().toISOString();
                const encryptedPassword = encryptor.encrypt(password + '|' + now.trim()).toString();
                if (!encryptedPassword) {
                  console.error("Encryption failed");
                  return; 
                }
                const response = await LoginService(email, isEncryptionEnabled ? encryptedPassword : password, organizationId, rememberLogin, returnUrl, authType, tenantId);
                if (response.access_token) {
                    setresponse(response);
                    userLocalData["oidc-info"] = JSON.stringify(response)
                    localStorage.setItem("access_token", response.access_token)
                    const userResponse = await GetUserDetails();
                    setUserDetails(userResponse ? userResponse.data : null);
                    const firstNameInitials = userResponse?.data.FirstName && userResponse?.data.FirstName ? userResponse?.data.FirstName.substring(0, 1).toUpperCase() : '';
                    const lastNameinitials = userResponse?.data && userResponse?.data.LastName ? userResponse?.data.LastName.substring(0, 1).toUpperCase() : '';
                    const finalData = firstNameInitials + lastNameinitials;
                    SAinitialsData = finalData;
                    localStorage.setItem("userType", userResponse?.data?.UserType ?? "");
                    userLocalData["user"] = JSON.stringify(userResponse?.data);
                    const orgDetails = await getOrganizationById(userResponse?.data?.OrganizationId ?? "");
                    userLocalData["orgDetails"] = JSON.stringify(orgDetails);

                    localStorage.setItem("userInfo", JSON.stringify(userLocalData))
                    navigate("/");
                } else {
                    setError(response.error_description);
                }
            }
        }
        catch (error) {
            console.error('Login failed:');
            setError('An unexpected error occurred.'); // Handle unexpected errors
        }
    };
    useEffect(() =>
    {
        const firstNameInitials = userDetails?.FirstName &&  userDetails?.FirstName ? userDetails?.FirstName.substring(0, 1).toUpperCase() : '';
        const lastNameinitials =  userDetails?.LastName &&  userDetails?.LastName ? userDetails?.LastName.substring(0, 1).toUpperCase() : '';
        const finalData = firstNameInitials + lastNameinitials;
        SAinitialsData = finalData;
    }, [userDetails])
    
    async function GetLoginUserInfo(userResponse : User) {
        try {
            const firstNameInitials =  userResponse?.FirstName &&  userResponse?.FirstName ? userResponse?.FirstName.substring(0, 1).toUpperCase() : '';
            const lastNameinitials =  userResponse &&  userResponse?.LastName ? userResponse?.LastName.substring(0, 1).toUpperCase() : '';
            const finalData = firstNameInitials + lastNameinitials;
            SAinitialsData = finalData;
            localStorage.setItem("userType", userResponse?.UserType ?? "");
        } catch (error) {
            console.error('Error fetching user or organization details', error);
        }
    }
    useEffect(() => {
        let token = localStorage.getItem("access_token");
		const userInfo = JSON.parse(localStorage.getItem("userInfo") || '{}');
		if (userInfo['oidc-info'] && userInfo['user']) {
			userDetails = JSON.parse(userInfo['user'])
			token = userInfo['oidc-info'].access_token;
		}
        if (token) {
            try {
                const loggedinUserInfo = jwt_decode<LoginUserInfo>(token);
                setLoginUserInfo(loggedinUserInfo);
                GetLoginUserInfo(userDetails);
                UserId = loggedinUserInfo.UserId;
            } catch (error) {
                signOut();
            }
        }
        else {
            signOut();
        }

    }, [user]);

    return (
    

        <Container maxWidth="sm" className="qadpt-superadminlogin">
            <Box mb={4} className="qadpt-brand-logo">
    <img 
        src={QuickAdopttext} 
        alt="QuickAdopt Logo" 
        className="qadpt-brand-logo-img"
    />
            </Box>

            {showSessionExpiredAlert && (
          <Alert 
            severity="error" 
            sx={{ 
              marginBottom: -5,
                width: '50%',
                position: 'relative',
                alignContent: 'center',
                textAlign: 'center', // centers the text
                display: 'flex',
                justifyContent: 'center',
              
            }}
          >
            Your session has expired. Please log in again.
          </Alert>
        )}

            {showSessionExpiredAlert && (
          <Alert 
            severity="error" 
            sx={{ 
              marginBottom: -5,
                width: '50%',
                position: 'relative',
                alignContent: 'center',
                textAlign: 'center', // centers the text
                display: 'flex',
                justifyContent: 'center',
              
            }}
          >
            Your session has expired. Please log in again.
          </Alert>
        )}

            <Box className="qadpt-welcome-message">
                <Typography variant="h4" className="qadpt-welcome-message-text">
                    Welcome back
                </Typography>
            </Box>

            <Box className="qadpt-login-form">
                <Typography className="qadpt-form-label">
                    Email
                </Typography>
                <TextField
                    required
                    fullWidth
                    type="email"
                    id="email"
                    name="Email"
                    autoComplete="Email"
                    autoFocus
                    value={email}
                    onChange={handleEmailChange}
                    placeholder="eg, <EMAIL>"
                    className="qadpt-custom-input"
                />

                <Typography className="qadpt-form-label">
                    Password
                </Typography>
                <TextField
                    required
                    fullWidth
                    type={showPassword ? "text" : "password"}
                    id="password"
                    name="password"
                    autoComplete="password"                    
                    value={password}
                    onChange={handlePasswordChange}
                    placeholder="Enter your password"
                    className="qadpt-custom-input"
                    InputProps={{
                        endAdornment: (
                            <InputAdornment position="end">
                                <IconButton
                                    aria-label="toggle password visibility"
                                    onClick={handleClickShowPassword}
                                    edge="end"
                                >
                                    {/* {showPassword ? <VisibilityOff /> : <Visibility />} */}
                                    <i className={`fal ${showPassword ? "fa-eye-slash" : "fa-eye"}`}></i>
                                </IconButton>
                            </InputAdornment>
                        ),
                    }}
                   
                />
                 {error && (
                    <FormHelperText error className="qadpt-text-danger">
                        {error}
                    </FormHelperText>
                )}

                <div className="qadpt-form-label">
                    <span onClick={() => window.open(`/forgotpassword`, '')}>
                        Forgot password?
                    </span>
                </div>
                <Button
                    type="button"
                    fullWidth
                    variant="contained"
                    className="qadpt-btn-default"
                    onClick={handleSubmit}
                > 
                        Continue
                </Button>
            </Box>

            <Box mt={12} className="qadpt-login-footer">
                <Typography variant="body2" className="qadpt-footer-text">
                    <Link href="/terms-of-use" className="qadpt-footer-link">Terms of use</Link> | 
                    <Link href="/privacy-policy" className="qadpt-footer-link">Privacy Policy</Link>
                </Typography>
            </Box>
            </Container>
      
    );


}
export default Login;






//Code based login changes

//   const { signIn ,signOut,loggedOut} = useAuth();
//   useEffect(() => {
//     // Get the user from userManager
//     userManager.getUser().then(user => {
//       if (!user || user.expired) {
//         // If the user is not authenticated or the token is expired, redirect to the identity server login page
//         loggedOut ? signOut() :userManager.signinRedirect() ;        
//       }
//       else {        
//         userManager.signinRedirect();
//       }
//     });
//   }, []);

//   return null;
// };